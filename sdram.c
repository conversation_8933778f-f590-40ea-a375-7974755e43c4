//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：sdram.c
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.21
//---------------------------------------------------------

#include "sdram.h"


#define SDRAM_EXAMPLE_PATTERN (0xA55A5AA5UL)
#define SDRAM_TEST_OFFSET 0x8000

#ifndef TIMER_CLOCK_NAME
#define TIMER_CLOCK_NAME clock_mchtmr0
#endif

#ifndef FEMC_CLOCK_NAME
#define FEMC_CLOCK_NAME clock_femc
#endif

uint32_t timer_freq_in_hz;

//hpm_stat_t rw_comparison(uint32_t start, uint32_t size_in_byte)
//{
//    hpm_stat_t status = status_success;
//    uint32_t i, delay = 0;

//    printf("comparison test: from 0x%x to 0x%x\n", start, start+size_in_byte);
//    for (i = 0; i < size_in_byte; i+=4)
//    {
//       *((uint32_t *)(start + i)) = (i << 24) | ((i + 1) << 16)
//                                | ((i + 2) << 8) | ((i + 3));
//    }
    
//    if (l1c_dc_is_enabled()) {
//        l1c_dc_writeback(start, size_in_byte);
//        printf("d-cached enabled and flushed\n");
//    } else {
//        printf("d-cached disabled\n");
//    }
//    for (i = 0; i < size_in_byte; i+=4) {
//       if(*((uint32_t *)(start + i)) != ((i << 24) | ((i + 1) << 16)| ((i + 2) << 8) | ((i + 3))))
//       {
//            printf("[%x] data mismatch @ 0x%lx: %lx\n", delay, start + i, *(uint32_t *)(start + i));
//            status = status_fail;
//            break;
//       }
//    }

//    printf("%s comparison %s\n", status == status_fail ? "!! " : "** ", status == status_fail ? "failed" : "succeeded");

//    return status;
//}

void rw_throughput(uint32_t start, uint32_t size_in_byte)
{
    uint64_t elapsed = 0, now;
    register uint32_t i, j;
    register uint32_t address = start;
    register uint32_t single_loop_byte;
    register uint32_t loop;

    if (start == BOARD_SDRAM_ADDRESS) {
        loop = 1;
        single_loop_byte = size_in_byte;
    } else {
        single_loop_byte = HPM_L1C_CACHE_SIZE << 2;
        loop = size_in_byte / single_loop_byte;
    }

    if (l1c_dc_is_enabled()) {
        printf("d-cached enabled\n");
        l1c_dc_flush(start, size_in_byte);
    } else {
        printf("d-cached disabled\n");
    }

    printf("read performance @0x%x, %uKB\n", start, size_in_byte >> 10);
    for (j = 0, elapsed = 0; j < loop; j++) {
        now = mchtmr_get_count(HPM_MCHTMR);
        for (i = 0; i < single_loop_byte; i += 4)
        {
            __asm volatile ("" : : "r" (*((uint32_t *)(address + i))));
        }
        elapsed += (mchtmr_get_count(HPM_MCHTMR) - now);
    }
    printf("read throughput: %.2f KB/s\n",
            (double) (size_in_byte >> 10) * timer_freq_in_hz / elapsed);

    printf("write performance @0x%x, %uKB\n", start, size_in_byte >> 10);
    for (j = 0, elapsed = 0; j < loop; j++) {
        now = mchtmr_get_count(HPM_MCHTMR);
        for (i = 0; i < single_loop_byte; i += 4)
        {
            (*((uint32_t *)(address + i))) = SDRAM_EXAMPLE_PATTERN;
        }
        elapsed += (mchtmr_get_count(HPM_MCHTMR) - now);
    }
    printf("write throughput: %.2f KB/s\n",
            (double) (size_in_byte >> 10) * timer_freq_in_hz / elapsed);

}

void init_fmc_pins(void)
{
    HPM_IOC->PAD[IOC_PAD_PD08].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D0
    HPM_IOC->PAD[IOC_PAD_PD05].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D1
    HPM_IOC->PAD[IOC_PAD_PD00].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D2
    HPM_IOC->PAD[IOC_PAD_PD01].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D3
    HPM_IOC->PAD[IOC_PAD_PD02].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D4
    HPM_IOC->PAD[IOC_PAD_PC27].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D5        
    HPM_IOC->PAD[IOC_PAD_PC28].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D6
    HPM_IOC->PAD[IOC_PAD_PC29].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D7
    HPM_IOC->PAD[IOC_PAD_PD04].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D8
    HPM_IOC->PAD[IOC_PAD_PD03].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D9
    HPM_IOC->PAD[IOC_PAD_PD07].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D10
    HPM_IOC->PAD[IOC_PAD_PD06].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D11
    HPM_IOC->PAD[IOC_PAD_PD10].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D12
    HPM_IOC->PAD[IOC_PAD_PD09].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D13
    HPM_IOC->PAD[IOC_PAD_PD13].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D14
    HPM_IOC->PAD[IOC_PAD_PD12].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_D15
    

    HPM_IOC->PAD[IOC_PAD_PC08].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A0
    HPM_IOC->PAD[IOC_PAD_PC09].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A1
    HPM_IOC->PAD[IOC_PAD_PC04].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A2
    HPM_IOC->PAD[IOC_PAD_PC05].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A3
    HPM_IOC->PAD[IOC_PAD_PC06].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A4
    HPM_IOC->PAD[IOC_PAD_PC07].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A5
    HPM_IOC->PAD[IOC_PAD_PC10].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A6
    HPM_IOC->PAD[IOC_PAD_PC11].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A7
    HPM_IOC->PAD[IOC_PAD_PC12].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A8
    HPM_IOC->PAD[IOC_PAD_PC17].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A9
    HPM_IOC->PAD[IOC_PAD_PC15].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A10
    HPM_IOC->PAD[IOC_PAD_PC21].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A11
    HPM_IOC->PAD[IOC_PAD_PC22].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_A12
   
      
    HPM_IOC->PAD[IOC_PAD_PC20].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_NOE  CS1
    HPM_IOC->PAD[IOC_PAD_PC25].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_NE2  CKE
    HPM_IOC->PAD[IOC_PAD_PC26].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_CLK
    HPM_IOC->PAD[IOC_PAD_PC30].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_NWE  DM0
    HPM_IOC->PAD[IOC_PAD_PC31].FUNC_CTL = IOC_PAD_FUNC_CTL_ALT_SELECT_SET(12);//FMC_NE1  DM1
}


void fmc_init(void)
{
    //if (!l1c_dc_is_enabled()) 
    //{
    //    l1c_dc_enable();
    //    l1c_dc_writeback(FMC_ADDRESS, FMC_DATA_SIZE);
    //}
    //printf("Dcache Enabled\n");

    //l1c_dc_disable();
}



