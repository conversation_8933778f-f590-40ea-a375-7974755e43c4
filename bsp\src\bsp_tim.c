#include "appmain.h"
#include "bsp_tim.h"
#include "timer.h"


uint32_t time_periodic_sec_cnt = 0;
uint32_t time_periodic_min_cnt = 0;
uint32_t time_periodic_hour_cnt = 0;
uint8_t time_sync_flag = 0;

uint32_t time_base_periodic_cnt = 0;

uint32_t time_base_100ms_periodic_cnt = 0;
uint32_t time_base_100ms_Flag = 0;

uint32_t time_base_20ms_periodic_cnt=0;
uint32_t time_base_20ms_Flag = 0;

void bsp_tim_init(void)
{
	/* HPM_GPTMR4 configuration */
	timer_Init();

	
	//HPM_GPTMR4 PWM
	Pwm_Init();
}




