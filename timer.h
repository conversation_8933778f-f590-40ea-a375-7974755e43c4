//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：timer.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.18
//---------------------------------------------------------

#ifndef _TIMER_H
#define _TIMER_H


#include <stdio.h>
#include "board.h"
#include "hpm_sysctl_drv.h"
#include "hpm_gptmr_drv.h"
#include "hpm_debug_console.h"


#define APP_BOARD_GPTMR               BOARD_GPTMR//HPM_GPTMR3
#define APP_BOARD_GPTMR_CH            BOARD_GPTMR_CHANNEL //1通道
#define APP_BOARD_GPTMR_IRQ           BOARD_GPTMR_IRQ//IRQn_GPTMR3
#define APP_BOARD_GPTMR_CLOCK         BOARD_GPTMR_CLK_NAME//clock_gptmr3

#define APP_TICK_MS                   (1) //定时器周期



#define APP_BOARD_PWM                 HPM_GPTMR4
#define APP_BOARD_PWM_CH              1
#define APP_BOARD_PWM_CLOCK           clock_gptmr4

void Pwm_Init(void);

void timer_Init(void);


#endif