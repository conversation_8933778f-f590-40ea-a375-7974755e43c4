//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：main.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.15
//---------------------------------------------------------
#include "board.h"
#include "hpm_gpio_drv.h"
#include "hpm_mchtmr_drv.h"
#include "pinmux.h"
#include "hpm_gpio_drv.h"
#include "hpm_gpiom_drv.h"
#include "hpm_sdmmc_sd.h"
#include "ff.h"
#include "diskio.h"
#include "deviceconfig.h"

#define GPIO_TOGGLE_COUNT 5
#define DEBOUNCE_THRESHOLD_IN_MS 150
#define MCHTMR_CLK_NAME (clock_mchtmr0)

extern uint8_t g_Com3WriteSdFlag;//写入SD卡文件COM3 二进制数据标志
extern uint8_t g_BB00WriteSdFlag;//写入SD卡文件BB00原始数据标志

extern FIL s_fileCOM3;

#define TEST_DIR_NAME "hpmicro_sd_test_dir0"

const char *show_error_string(FRESULT fresult);


void  Fatfs_Init(void);


void DeleteFileFromSd(uint8_t FlieType);//从SD卡删除文件

void SdFileOperateTypeSet(uint8_t OperateType,uint8_t FlieType);//SD卡文件系统操作类型处理

void ReadFileOpenFromSd(uint8_t FlieType);//从SD卡打开数据文件，用于读属性

void WriteFileOpenFromSd(uint8_t FlieType);//从SD卡打开数据文件，用于写属性

void WriteFileToSd(uint8_t FlieType);//写入文件到SD卡

void ReadFileToSd(uint8_t FlieType);//从SD卡读取文件

void CloseFileToSd(uint8_t FlieType);//从SD卡读取完毕后，关闭文件

void FormatSD(void);//格式化sd卡

void SdFileWriteOperate(void);//SD卡文件系统写操作

void SdFileReadOperate(void);//SD卡文件系统读操作


void WriteCOM3FileToSd(void);//COM3二进制数据写入文件到SD卡

void WriteBB00FileToSd(void);//BB00原始数据写入文件到SD卡


void SdFileTest(void);//SD卡文件测试程序

void Exti_Init(void);
void Led_Control(void);