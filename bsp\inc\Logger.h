#ifndef ____LOGGER_H____
#define ____LOGGER_H____


#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "board.h"
#include "hpm_debug_console.h"
#include "hpm_l1c_drv.h"
#include "hpm_romapi.h"
#include "hpm_clock_drv.h"

typedef struct log_buf_t
{
	char* pbuf;
	uint32_t nSize;
}LogBufTypeDef;

typedef enum log_type_t
{
	Log_Type_0 = 0,
	Log_Type_1 = 1,
	Log_Type_2 = 2,
	Log_Type_3 = 3,
	Log_Type_4 = 4,
	Log_Type_5 = 5,
	Log_Type_6 = 6,
	Log_Type_7 = 7,
	Log_Type_MAX,
}LogTypeEnumDef;

#define LOG_BUF_SIZE	512

#define NATURE_CONSTANT_e		2.718281828459045 			//e

extern char g_charBuf[];

/****************************************************************************
* 	:	generateCSVLogFileName
* 		:	char* fileName, 		
* 	:	int,  1 fail   0 success
****************************************************************************
* 	:	
****************************************************************************/
int generateCSVLogFileName(char* fileName);
/****************************************************************************
* 	:	writeCSVLog
* 		:	unsigned char* filename,	
* 		:	LogBufTypeDef* pLog		
* 	:	int,  1 fail   0 success
****************************************************************************
* 	:	
****************************************************************************/
int writeCSVLog(unsigned char* filename, LogBufTypeDef* pLog);
/****************************************************************************
* 	:	synthesisLogBuf
* 		:	uint8_t* pBuf, 			
* 		:	uint32_t nSize, 		
* 		:	uint16_t type, 			
* 		:	LogBufTypeDef* pLog		
* 	:	int,  1 fail   0 success
****************************************************************************
* 	:	, 
****************************************************************************/
int synthesisLogBuf(uint8_t* pBuf, uint32_t nSize, uint16_t type, LogBufTypeDef* pLog);


int parseFPGABuff(float* vAngle, float* vGyro, float* vAcc, float* lat, float* lon,float* height, \
					float* speed_nor,float* speed_est,float* speed_earth, uint8_t* state, float* time, uint32_t* week,\
						float*LonStd, float* LatStd, float* AltitudeStd, uint8_t* StarNum, \
					float* vn_std,float* ve_std, float* vd_std,float* rollstd,float* pitchstd,float* yawstd,float* temper,\
						uint16_t* wheelState,uint8_t* GPSSpeedState,uint16_t* ResolveState);

int writeCSVFileHead(void);
					
#endif // ____LOGGER_H____
