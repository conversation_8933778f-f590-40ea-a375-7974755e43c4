/*!
    \file  main.h
    \brief the header file of main 
*/

/*
    Copyright (C) 2016 GigaDevice

    2016-08-15, V1.0.0, firmware for GD32F4xx
*/

#ifndef __FPGAD_H
#define __FPGAD_H

#include <stdio.h>
#include "board.h"
#include "hpm_sysctl_drv.h"
#include "hpm_gptmr_drv.h"
#include "hpm_debug_console.h"
#include "appdefine.h"



#pragma pack(1)
//惯导FEMC通讯协议 V1.23
typedef struct _fpgadata {
	unsigned short	DATA_LEN;	//1//
	unsigned short	NUM_CLK;	//2//
	unsigned short	VERSION;	//3//

	short	UNOtemperature;		//4//
	short	DUEtemperature;		//5
	short	rateX_data;		//6
	short	rateY_data;		//7
	short	rateZ_data;		//8
	short	accX_data;		//9
	short	accY_data;		//10
	short	accZ_data;		//11

	short	magGrp0;		//12 x轴磁力计
	short	magGrp1;		//13 y轴磁力计
	short	magGrp2;		//14 Z轴磁力计

	int fog_x;			//15
	int fog_y;			//17
	int fog_z;			//19
	unsigned short	fog_tempx;	//21 X轴光纤陀螺温度
	unsigned short	fog_tempy;	//22 y轴光纤陀螺温度
	unsigned short	fog_tempz;	//23 z轴光纤陀螺温度
	float axis3_accx;               //24 X轴加速度
	float axis3_accy;		//26 y轴加速度
	float axis3_accz;		//28 z轴加速度
	unsigned short	axis3_temp;	//30 石英加速度计温度

	unsigned short	reserved[3];	//31 ~ 33

	unsigned short	hGPSData_gpsweek;   //34 GNSS周
	unsigned int	hGPSData_gpssecond; //35 GNSS周内秒 GNSS的AGRIC
	unsigned int	gpssecond982;	    //37 GNSS周内秒 gps本身更新的周内秒
	unsigned int	ppstimesdelay;	    //39 Times_dy

	unsigned short	GPGGA_STAR;	//41 GNSS搜星数
	unsigned short	rtkstatus;	//42
	unsigned short	gnssspeedstatus;//43 GNSS速度状态
	unsigned short	GPRMC_TRA[3];	//44~46 真北航迹方向


	float	hGPSData_vn;		//47
	float	hGPSData_ve;		//49
	float	hGPSData_vu;		//51

	//unsigned short	gnsspositionstaus;//53 位置状态 ---
	//unsigned short directionofLat;    //54 纬度方向 ---
        unsigned short	GnssStaDirLat;//53 高8位位置状态,低8位纬度方向

	double	hGPSData_Lat;		//54

	//unsigned short directionofLon; //59 经度方向---
        unsigned short DirLonHeadingSta; //58 高8位经度方向,低8位航向状态

	double	hGPSData_Lon;		//59
	double	hGPSData_Alt;		//63

	//unsigned short headingstate;	//68 航向状态---

	float baselinelength;		//67

	float	hGPSData_Roll;		//69
	float	hGPSData_Pitch;		//71
	float	hGPSData_Yaw;		//73

#if 1	//10/10 append
	float ECEF_X;		//75
	float ECEF_Y;		//77
	float ECEF_Z;		//79
	float geometry_z;	//81
	float location_z;	//83
	float time_z;		//85
	float vertical_z;	//87
	float horizontal_z;	//89
	float north_z;		//91
	float east_z;		//93 东向精度因子，Z轴
	float endheight_z;	//95 截至高度角，Z轴
#endif
        float StanDeviat_Lat;    //97 纬度标准差
        float StanDeviat_Lon;    //99 经度标准差
        float StanDeviat_Alt;    //101 高度标准差
        float StanDeviat_Heading;//103 航向标准差
        float StanDeviat_Pitch;  //105 俯仰标准差

        int   Sol_Status;        //107 解状态
        int   Pos_Type;          //109 位置类型

	unsigned short	checksum; //111

	unsigned short	checksumgd;     //112 CRC校验，累加和（GD端
	unsigned short	packetindex;	//113 自检，帧累计值（GD端）

	//The following are the results of the algorithm
	double	Alongitude;	//
	double	Alatitude;	//
	float	Aaltitude;	//
	float	Ave;		//
	float	Avn;		//
	float	Avu;		//
	float	Apitch;		//
	float	Aroll;		//
	float	Aheading;	//
	unsigned short	checksumA;//
} fpgadata_t;

typedef struct
{
	unsigned short	head1;			//BB11
	unsigned short	head2;			//DBBD
	unsigned short  dataLen;		//
    fpgadata_t fpgadata;			//FPGA
    unsigned int fpgaItrCount;		//FPGA
    unsigned int fpgaLoopCount;		//FPGA
	unsigned short Status;			//
	unsigned short CheckSum;		//
} FpgadataSend_t;

typedef struct
{
    float timestamp;								
	float WheelSpeed_Front_Left;		
    float WheelSpeed_Back_Left;			
    float WheelSpeed_Front_Right;		
    float WheelSpeed_Back_Right;		
    float WheelSteer;								
    float OdoPulse_1;								
    float OdoPulse_2;								
    unsigned char Gear;							

} CanData_t;

typedef struct
{
    CanData_t data;
    unsigned int counter;
	unsigned char flag;
} CanDataTypeDef;
#pragma pack()


extern	fpgadata_t		gpagedata;
extern	CanDataTypeDef	gcanInfo;

extern FpgadataSend_t gfpgadataSend;
											//FPGApredo


extern unsigned short adlxdata[12];


#define	U4RX_MAXCOUNT		(1024 * 4)	//(1024 * 4)
#define	FRAMEPARSEBUFSIZE	(512 * 2)
extern	unsigned char grxbuffer[U4RX_MAXCOUNT + 2];
extern	int grxlen, grxst;
//extern	unsigned char gframeParsebuf[FRAMEPARSEBUFSIZE];
//extern	can_receive_message_struct gCanRxBuf;


//extern	void printf_uart4(int type, char *fmt, ...);
//extern	void uart4sendmsg(char *txbuf, int size);
//extern	void uart4sendmsg_billdebug(char *txbuf, int size);
//extern	void uart4sendmsg_canout(can_receive_message_struct *receive_message);
//extern	void analysisRxdata(void);

#endif /* __FPGAD_H */


