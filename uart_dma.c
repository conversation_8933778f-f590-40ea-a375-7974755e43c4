/*
 * Copyright (c) 2022-2025 HPMicro
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */

#include <stdlib.h>
#include <stdio.h>
#include <stdbool.h>
#include <string.h>
#include "hpm_soc.h"
#include "hpm_clock_drv.h"

#include "hpm_l1c_drv.h"
#include "hpm_common.h"
#include "uart_dma.h"
#include "uart.h"


 ATTR_PLACE_AT_FAST_RAM_NON_INIT_WITH_ALIGNMENT(4) static bool dma_done = true;

static int uart_dma_tx_send(void* handle, uint8_t *data, uint32_t len)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    uint8_t des_index = 0;
    dma_channel_config_t first_tx_dma_ch_config;
    dma_channel_config_t dma_ch_config;
    dma_default_channel_config(ctx->dma_base, &dma_ch_config);

    dma_ch_config.size_in_byte = len;
    dma_ch_config.src_addr = core_local_mem_to_sys_address(ctx->core_id, (uint32_t)data);
    dma_ch_config.dst_addr = (uint32_t)&(ctx->uart_base->THR);
    dma_ch_config.src_width = DMA_TRANSFER_WIDTH_BYTE;
    dma_ch_config.dst_width = DMA_TRANSFER_WIDTH_BYTE;
    dma_ch_config.src_burst_size = DMA_NUM_TRANSFER_PER_BURST_1T;
    dma_ch_config.src_mode = DMA_HANDSHAKE_MODE_NORMAL;
    dma_ch_config.dst_mode = DMA_HANDSHAKE_MODE_HANDSHAKE;
    dma_ch_config.src_addr_ctrl = DMA_ADDRESS_CONTROL_INCREMENT;
    dma_ch_config.dst_addr_ctrl = DMA_ADDRESS_CONTROL_FIXED;
    dma_ch_config.interrupt_mask = DMA_INTERRUPT_MASK_ALL;
    dma_ch_config.linked_ptr = core_local_mem_to_sys_address(ctx->core_id, (uint32_t)&ctx->tx_descriptor[des_index + 1]);
    dma_config_linked_descriptor(ctx->dma_base, &ctx->tx_descriptor[des_index], ctx->dma_tx_ch, &dma_ch_config);
    if (des_index == 0)
    {
        memcpy(&first_tx_dma_ch_config, &dma_ch_config, sizeof(dma_channel_config_t));
    }
    des_index++;

    dma_default_channel_config(ctx->dma_base, &dma_ch_config);
    dma_ch_config.size_in_byte = 1;
    dma_ch_config.src_addr = core_local_mem_to_sys_address(ctx->core_id, (uint32_t)&dma_done);
    dma_ch_config.dst_addr = core_local_mem_to_sys_address(ctx->core_id, (uint32_t)(ctx->tx_done));
    dma_ch_config.src_width = DMA_TRANSFER_WIDTH_BYTE;
    dma_ch_config.dst_width = DMA_TRANSFER_WIDTH_BYTE;
    dma_ch_config.src_burst_size = DMA_NUM_TRANSFER_PER_BURST_1T;
    dma_ch_config.src_mode = DMA_HANDSHAKE_MODE_NORMAL;
    dma_ch_config.dst_mode = DMA_HANDSHAKE_MODE_NORMAL;
    dma_ch_config.src_addr_ctrl = DMA_ADDRESS_CONTROL_FIXED;
    dma_ch_config.dst_addr_ctrl = DMA_ADDRESS_CONTROL_FIXED;
    dma_ch_config.interrupt_mask = DMA_INTERRUPT_MASK_ALL;
    dma_ch_config.linked_ptr = NULL;
    dma_config_linked_descriptor(ctx->dma_base, &ctx->tx_descriptor[des_index], ctx->dma_tx_ch, &dma_ch_config);
    des_index++;

    if (status_success != dma_setup_channel(ctx->dma_base, ctx->dma_tx_ch, &first_tx_dma_ch_config, true))
    {
        printf("tx dma setup channel failed 0\n");
        return -2;
    }
    return 0;
}

static uint32_t uart_get_current_recv_remaining_size(void* handle)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    return dma_get_remaining_transfer_size(ctx->dma_base, ctx->dma_rx_ch);
}

static int uart_rx_dma_autorun(void* handle)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    dma_channel_config_t dma_ch_config;
    dma_default_channel_config(ctx->dma_base, &dma_ch_config);
    dma_ch_config.size_in_byte = ctx->rx_size;
    dma_ch_config.src_addr = (uint32_t)&ctx->uart_base->RBR;
    dma_ch_config.dst_addr = core_local_mem_to_sys_address(ctx->core_id, (uint32_t)(ctx->rx_buff));
    dma_ch_config.src_width = DMA_TRANSFER_WIDTH_BYTE;
    dma_ch_config.dst_width = DMA_TRANSFER_WIDTH_BYTE;
    dma_ch_config.src_burst_size = DMA_NUM_TRANSFER_PER_BURST_1T;
    dma_ch_config.src_mode = DMA_HANDSHAKE_MODE_HANDSHAKE;
    dma_ch_config.dst_mode = DMA_HANDSHAKE_MODE_NORMAL;
    dma_ch_config.src_addr_ctrl = DMA_ADDRESS_CONTROL_FIXED;
    dma_ch_config.dst_addr_ctrl = DMA_ADDRESS_CONTROL_INCREMENT;
    dma_ch_config.interrupt_mask = DMA_INTERRUPT_MASK_ALL;
    dma_ch_config.linked_ptr = core_local_mem_to_sys_address(ctx->core_id, (uint32_t)&ctx->rx_descriptor[0]);
    dma_config_linked_descriptor(ctx->dma_base, &ctx->rx_descriptor[0], ctx->dma_rx_ch, &dma_ch_config);
    if (status_success != dma_setup_channel(ctx->dma_base, ctx->dma_rx_ch, &dma_ch_config, true))
    {
        return -2;
    }
    return 0;
}

static int uart_tx_dma(void* handle, uint8_t *data, uint32_t len)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    // dma addr must 4byte aligned
    if (((uint32_t)data & 0x3) != 0)
        return -1;
    if (get_uart_tx_idle(handle))
    {
        *(bool*)(ctx->tx_done) = false;
        return uart_dma_tx_send(handle, data, len);
    }
    return -2;
}

uint32_t uart_dma_recv_polling(void* handle, uint8_t *data, uint32_t maxsize)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    uint32_t size = 0;
    uint32_t old_recvlen = ctx->old_rxlen;
    uint32_t new_recvlen = ctx->rx_size - uart_get_current_recv_remaining_size(handle);
    if (old_recvlen != new_recvlen)
    {
        if (new_recvlen > old_recvlen)
        {
            size = new_recvlen - old_recvlen;
			
            if (size > maxsize)
            {
                printf("BAD! overflow1, uart_base:0x%08x, bufsize:%d, old:%d, new:%d, size:%d, maxsize:%d!\r\n",(uint32_t)ctx->uart_base, ctx->rx_size, old_recvlen, new_recvlen, size, maxsize);
                size = maxsize;
            } 
            memcpy(data, ctx->rx_buff + old_recvlen, size);
        }
        else
        {
            size = ctx->rx_size - old_recvlen;
            if (size > maxsize)
            {
                printf("BAD! overflow2, old:%d, new:%d, size:%d, maxsize:%d!\r\n", old_recvlen, new_recvlen, size, maxsize);
                size = maxsize; 
                memcpy(data, ctx->rx_buff + old_recvlen, size);
            }
            else if (size + new_recvlen > maxsize)
            {
                printf("BAD! overflow3, old:%d, new:%d, size:%d, maxsize:%d!\r\n", old_recvlen, new_recvlen, size, maxsize);
                memcpy(data, ctx->rx_buff + old_recvlen, size);
                memcpy(data + size, ctx->rx_buff, maxsize - size);
                size = maxsize;
				
            }
            else
            {
                memcpy(data, ctx->rx_buff + old_recvlen, size);
                memcpy(data + size, ctx->rx_buff, new_recvlen);
                size += new_recvlen;
            }
        }
		
        ctx->old_rxlen = new_recvlen;
        return size;
    }
    return 0;
}

bool get_uart_tx_idle(void *handle)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    return *(bool*)(ctx->tx_done);
}

int uart_dma_output(void* handle, uint8_t *data, uint16_t length)
{
    if (data == NULL || length <= 0)
        return -1;
    return uart_tx_dma(handle, data, length);
}

int uart_dma_init(void* handle)
{
    uart_dma_ctx_t *ctx = (uart_dma_ctx_t*)handle;
    hpm_stat_t stat;
    uart_config_t config = {0};
    uart_default_config(ctx->uart_base, &config);
    if(!dma_done)
    {
        dma_done = true;
    }
    *(bool*)(ctx->tx_done) = true;

    intc_m_disable_irq(ctx->dmairq);
    dmamux_config(ctx->dmamux_base, DMA_SOC_CHN_TO_DMAMUX_CHN(ctx->dma_base, ctx->dma_tx_ch), ctx->dmamux_tx_src, true);
    dmamux_config(ctx->dmamux_base, DMA_SOC_CHN_TO_DMAMUX_CHN(ctx->dma_base, ctx->dma_rx_ch), ctx->dmamux_rx_src, true);
    if(uart_rx_dma_autorun(handle) != 0)
    {
        return -2;
    }

    config.fifo_enable = true;
    config.dma_enable = true;
    config.tx_fifo_level = uart_tx_fifo_trg_not_full;
    config.rx_fifo_level = uart_rx_fifo_trg_not_empty;
    config.src_freq_in_hz = ctx->uart_src_freq;
    config.baudrate = ctx->baudrate;
    stat = uart_init(ctx->uart_base, &config);
    if (stat != status_success)
    {
        printf("failed to initialize uart\n");
        return -3;
    }
    return 0;
}
