#include "ins.h"
#include "CONST.h"

AHRS_ST ahrs_k={0};

bool ahrs_titl_alig = false;
//const ftype WIE = 360.0/(24*3600.0);

static ftype twoant_mean = 0.0;

static uint16_t imu_sample_cnt    = 0;
static uint16_t twoant_sample_cnt = 0;

static uint16_t mag_sample_cnt = 0;
static V3_ST mag_mean = {0};

static uint16_t imu_sample_count = 0;
static V3_ST wie_mean = {0};
static V3_ST acc_mean = {0};
  
void ahrs_init(SENS_RAW_ST *psins_raw,MAG_RAW_ST *pmag_raw,GNSS_RAW_ST *pgnss_raw,uint16_t init_cnt)
{
    bool twoAntYaw_update    = false;
    bool twoant_yaw_have_get = false;
    bool mag_yaw_have_get    = false;

    ftype twoant_meas_yaw = 0.0;
    V3_ST wie_ned = {0};
    V3_ST mag_ned = {0};

    ftype acc_norm = 0.0;
    ftype wnorm = 0.0;

    ftype dyaw = 0.0;
    ftype last_gnss_heading = 0.0;

    ftype vecLength = 0.0;
    ftype roll= 0.0,pitch= 0.0,yaw= 0.0;
    ftype sin_roll= 0.0,cos_roll= 0.0,sin_pitch= 0.0,cos_pitch= 0.0;
    ftype gyro_meas_yaw = 0.0;
    ftype mag_meas_yaw = 0.0;

    ftype u1 = 0.0,u2 = 0.0,u3 = 0.0,u4 = 0.0,u5 = 0.0,u6 = 0.0;
    ftype q0 = 0.0,q1 = 0.0,q2 = 0.0,q3 = 0.0;

	//if(fabs(pgnss_raw->bl - TWOANTBL)<=0.02  && pgnss_raw->hstate==4)//
			if(pgnss_raw->hstate==4)
    {
        twoAntYaw_update =true;
    }
    else
    {
        twoAntYaw_update =false;
    }

    if(pgnss_raw->twoAntYaw_update !=true)//
    {
        twoAntYaw_update =false;
    }

    if(imu_sample_cnt < init_cnt)//imu30
    {
        //--------------------------------------------------
         if(twoAntYaw_update == true)//
         {
            twoant_meas_yaw = pgnss_raw->heading;//
            if(twoant_meas_yaw>=0.0 && twoant_meas_yaw<=360)
            {
                if(twoant_sample_cnt == 0)twoant_mean=0;

                twoant_mean       = twoant_mean + twoant_meas_yaw;
                twoant_sample_cnt = twoant_sample_cnt + 1;

                if(last_gnss_heading != 0)dyaw = twoant_meas_yaw - last_gnss_heading;

                if(fabs(dyaw)>0.1)//GNSS
                {
                        twoant_mean = twoant_meas_yaw;
                        twoant_sample_cnt  = 1;
                }

                last_gnss_heading = twoant_meas_yaw;
            }
            else
            {
            
            }
         }
         else
         {
         
         }         
        //--------------------------------------------------

        //------------------mag---------------
        if(mag_sample_cnt == 0)//
        {
            mag_mean.x = 0;
            mag_mean.y = 0;
            mag_mean.z = 0;
        } 

        //
        //if(mag_raw.magYaw_update == true)
        {
            //mag_mean.x = mag_mean.x + mag_raw.mag.x;
            //mag_mean.y = mag_mean.y + mag_raw.mag.y;
           // mag_mean.z = mag_mean.z + mag_raw.mag.z;
            
            //
            mag_sample_cnt = mag_sample_cnt+1;
        }
        //------------------mag---------------

        //------------------imu---------------
         if(imu_sample_count==0)
         {
            wie_mean.x  = 0;
            wie_mean.y  = 0;
            wie_mean.z  = 0;
                
            acc_mean.x = 0;
            acc_mean.y = 0;
            acc_mean.z = 0;          
         } 

        //
		wie_mean.x = wie_mean.x + psins_raw->gyro.x;
		wie_mean.y = wie_mean.y + psins_raw->gyro.y;
		wie_mean.z = wie_mean.z + psins_raw->gyro.z;

        //IMU
		acc_mean.x = acc_mean.x + psins_raw->acc.x;
		acc_mean.y = acc_mean.y + psins_raw->acc.y;
		acc_mean.z = acc_mean.z + psins_raw->acc.z;

        //		    
        imu_sample_count = imu_sample_count + 1;

        //
        acc_norm = sqrt(SQR(psins_raw->acc.x) + SQR(psins_raw->acc.y) + SQR(psins_raw->acc.z));
        wnorm = sqrt(SQR(psins_raw->gyro.x) + SQR(psins_raw->gyro.y) + SQR(psins_raw->gyro.z));
        if(acc_norm < 0.9 || acc_norm > 1.1 || wnorm >20*WIE)
        {
            acc_mean.x =  psins_raw->acc.x;
	        acc_mean.y =  psins_raw->acc.y;
	        acc_mean.z =  psins_raw->acc.z;

            wie_mean.x =  psins_raw->gyro.x;
		    wie_mean.y =  psins_raw->gyro.y;
		    wie_mean.z =  psins_raw->gyro.z;

            imu_sample_count = 1;
        }

        imu_sample_cnt = imu_sample_cnt+1;//
        //------------------imu---------------

        ahrs_k.tilt_complete = false;//AHRS
        ahrs_k.yaw_complete   = false;
    }
    else
    {
        //--------------------------------------------------
        if(twoAntYaw_update == true)//	
        {
		    twoant_meas_yaw = pgnss_raw->heading;//
                
            if(twoant_meas_yaw>=0.0 && twoant_meas_yaw<=360)//0~2*PI
            {
                //atan2(y,x)-PI~+PI
			    //-PI~+PI3600
                if(twoant_sample_cnt == 0)twoant_mean = 0;

                twoant_mean       = twoant_mean + twoant_meas_yaw;
                twoant_sample_cnt = twoant_sample_cnt + 1;

                dyaw = 0.0;
                if(last_gnss_heading != 0)dyaw = twoant_meas_yaw - last_gnss_heading;               
                if(fabs(dyaw)>0.1)//GNSS
                {
                    twoant_mean        = twoant_meas_yaw;
                    twoant_sample_cnt  = 1;
                }

                last_gnss_heading = twoant_meas_yaw;

                //twoant_yaw_have_get = true;
            }
            else
            {
                //twoant_yaw_have_get = false;
            }
         }
         else
         {
            //twoant_yaw_have_get = false;
         }
        //--------------------------------------------------
        
        //-------------------------------
        if(mag_sample_cnt == 0)//
        {
            mag_mean.x = 0;
            mag_mean.y = 0;
            mag_mean.z = 0;
        }  

		//
        //if(mag_raw.magYaw_update == true)
        //{
		//    mag_mean.x = mag_mean.x + mag_raw.mag.x;
		//    mag_mean.y = mag_mean.y + mag_raw.mag.y;
		//    mag_mean.z = mag_mean.z + mag_raw.mag.z;
        
		    //
		//    mag_sample_cnt = mag_sample_cnt+1;
        // } 
        //-------------------------------

        //------------------AHRS-----------
        wie_mean.x = wie_mean.x / (double)imu_sample_count;
		wie_mean.y = wie_mean.y / (double)imu_sample_count;
		wie_mean.z = wie_mean.z / (double)imu_sample_count;

        acc_mean.x = acc_mean.x / (double)imu_sample_count;
		acc_mean.y = acc_mean.y / (double)imu_sample_count;
		acc_mean.z = acc_mean.z / (double)imu_sample_count;

        bool gyro_yaw_have_get = true;

        if(twoant_sample_cnt>0)
        {
            twoant_mean = twoant_mean/(double)twoant_sample_cnt;
            twoant_yaw_have_get = true;
        }
        else
        {
            twoant_mean = 0;
            twoant_yaw_have_get = false;
        }

        //0.3s
        if(mag_sample_cnt > 0)
        {
		    mag_mean.x = mag_mean.x / mag_sample_cnt;
		    mag_mean.y = mag_mean.y / mag_sample_cnt;
		    mag_mean.z = mag_mean.z / mag_sample_cnt; 

            mag_yaw_have_get = true;
        }
        else
        {
            mag_yaw_have_get = false;
        } 

        vecLength = sqrt(acc_mean.x*acc_mean.x + acc_mean.y*acc_mean.y + acc_mean.z*acc_mean.z);
        roll  = atan2(-acc_mean.y,-acc_mean.z);
		pitch = asin(acc_mean.x/vecLength);

        if(twoant_yaw_have_get == true)//AHRS
        {
            yaw = twoant_mean*DEG2RAD;
            ahrs_k.yaw_complete  = true;
        }
        else if(gyro_yaw_have_get == true)//AHRS
        {
            sin_roll  =  sin(roll); cos_roll  =  cos(roll);
		    sin_pitch =  sin(pitch);cos_pitch =  cos(pitch);

            //wie_ned = Cbn*wie_b
		    wie_ned.x = cos_pitch*wie_mean.x + sin_pitch*sin_roll*wie_mean.y + cos_roll*sin_pitch*wie_mean.z;
		    wie_ned.y = cos_roll*wie_mean.y  - sin_roll*wie_mean.z;

            if(fabs(wie_ned.x)<1e-12)//wnx==0
            {
                gyro_meas_yaw =  0.5*PI - atan2(wie_ned.x , -wie_ned.y);
            }
            else//wnx !=0
            {
                gyro_meas_yaw =  atan2(-wie_ned.y , wie_ned.x);
            }

            yaw = gyro_meas_yaw;
            ahrs_k.yaw_complete  = true;
        }
        else if(mag_yaw_have_get==true) //
        {
            sin_roll  =  sin(roll); cos_roll  =  cos(roll);
		    sin_pitch =  sin(pitch);cos_pitch =  cos(pitch);

            //mned = Cbn*mb
            mag_ned.x = cos_pitch*mag_mean.x + sin_pitch*sin_roll*mag_mean.y + cos_roll*sin_pitch*mag_mean.z;
            mag_ned.y = cos_roll*mag_mean.y  - sin_roll*mag_mean.z;

            //
            //90
            mag_meas_yaw =  atan2(-mag_ned.y , mag_ned.x);

            yaw = mag_meas_yaw;
            ahrs_k.yaw_complete  = true;
        }
        else//0
        {
            yaw = 0;
            ahrs_k.yaw_complete  = false;
        }

        if(yaw > PI)//-PI~+PI
        {
            yaw =  yaw - 2*PI;
        }
        else if(yaw < -PI)
        {
            yaw = yaw + 2*PI;
        }
        //------------------AHRS-----------

        //----------AHRS--------------------------
        u1 = cos(0.5*roll);
        u2 = cos(0.5*pitch);
        u3 = cos(0.5*yaw);
    
        u4 = sin(0.5*roll);
        u5 = sin(0.5*pitch);
        u6 = sin(0.5*yaw);
    
		ahrs_k.q0 = u1*u2*u3  +  u4*u5*u6;
		ahrs_k.q1 = u4*u2*u3  -  u1*u5*u6;
		ahrs_k.q2 = u1*u5*u3  +  u4*u2*u6;
		ahrs_k.q3 = u1*u2*u6  -  u4*u5*u3; 

        acc_mean.x = acc_mean.x/vecLength;
        acc_mean.y = acc_mean.y/vecLength;
        acc_mean.z = acc_mean.z/vecLength;
        //----------AHRS--------------------------

        //ahrs,AHRS
		ahrs_k.tilt_complete = true;

        //----------------------------------------------------------------------
        q0 = ahrs_k.q0; q1 = ahrs_k.q1; q2 = ahrs_k.q2; q3 = ahrs_k.q3;
            
        ahrs_k.pitch = RAD2DEG*asin(-2*(q1*q3 - q0*q2));
        ahrs_k.roll  = RAD2DEG*atan2(2*(q2*q3 + q0*q1), q0*q0 - q1*q1 - q2*q2 + q3*q3);
        ahrs_k.yaw   = RAD2DEG*atan2(2*(q1*q2 + q0*q3),q0*q0 + q1*q1 - q2*q2 - q3*q3);

        ahrs_k.acc_pitch = RAD2DEG*pitch;
        ahrs_k.acc_roll  = RAD2DEG*roll;
        ahrs_k.acc_yaw   = RAD2DEG*yaw;
    }

    ahrs_k.cnt = 0;//ahrs_update
}

//gyro---
//acc----
static V3_ST dAng = {0};
void ahrs_imu_update(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw)
{
    ftype beta = 0.02;
    ftype dt = psins_raw->dt;
    V3_ST prevdAng    = {0};
   
    VNED_ST wie_ned  = {0};
    V3_ST wieb = {0};

    ftype q0 = 0.0,q1 = 0.0,q2 = 0.0,q3 = 0.0;
    ftype Cnbk_1[3][3] = {0};

    V3_ST gyro = {0};
    V3_ST dAng0xdAng1 = {0};
    V3_ST phi = {0};
    Q4_ST qh = {0};
    Q4_ST quat_new = {0};

    ftype recipNorm = 0.0;

    V3_ST acc = {0};

    ftype s0 = 0.0,s1 = 0.0,s2 = 0.0,s3 = 0.0;
    ftype ax = 0.0,ay = 0.0,az = 0.0;

    prevdAng.x = dAng.x;//
    prevdAng.y = dAng.y;
    prevdAng.z = dAng.z;

    //0
    ////
    psins_raw->gyro_const_bias.x = 0.0;
    psins_raw->gyro_const_bias.y = 0.0;
    psins_raw->gyro_const_bias.z = 0.0;

    //
    if(pgnss_raw->lat != 0)
    {
        wie_ned.n =  WIE*cos(pgnss_raw->lat*DEG2RAD);
        wie_ned.e =  0.0;
        wie_ned.d = -WIE*sin(pgnss_raw->lat*DEG2RAD);
    }
    else
    {
        wie_ned.n = 0.0;
        wie_ned.e = 0.0;
        wie_ned.d = 0.0;
    }

    q0 = ahrs_k.q0; 
    q1 = ahrs_k.q1; 
    q2 = ahrs_k.q2; 
    q3 = ahrs_k.q3;

    Cnbk_1[0][0] = q0*q0 + q1*q1 - q2*q2 - q3*q3;
    Cnbk_1[0][1] = 2*(q1*q2 + q0*q3);
    Cnbk_1[0][2] = 2*(q1*q3 - q0*q2);

    Cnbk_1[1][0] = 2*(q1*q2 - q0*q3);
    Cnbk_1[1][1] = q0*q0 - q1*q1 + q2*q2 - q3*q3;
    Cnbk_1[1][2] = 2*(q2*q3 + q0*q1);

    Cnbk_1[2][0] = 2*(q1*q3 + q0*q2);
    Cnbk_1[2][1] = 2*(q2*q3 - q0*q1);
    Cnbk_1[2][2] = q0*q0 - q1*q1 - q2*q2 + q3*q3;

    wieb.x = Cnbk_1[0][0]*wie_ned.n  + Cnbk_1[0][1]*wie_ned.e + Cnbk_1[0][2]*wie_ned.d;
	wieb.y = Cnbk_1[1][0]*wie_ned.n  + Cnbk_1[1][1]*wie_ned.e + Cnbk_1[1][2]*wie_ned.d;
	wieb.z = Cnbk_1[2][0]*wie_ned.n  + Cnbk_1[2][1]*wie_ned.e + Cnbk_1[2][2]*wie_ned.d;

    //wieb.x = 0.0;
    //wieb.y = 0.0;
    //wieb.z = 0.0;

    gyro.x = (psins_raw->gyro.x - psins_raw->gyro_const_bias.x - wieb.x)*DEG2RAD;
    gyro.y = (psins_raw->gyro.y - psins_raw->gyro_const_bias.y - wieb.y)*DEG2RAD;
    gyro.z = (psins_raw->gyro.z - psins_raw->gyro_const_bias.z - wieb.z)*DEG2RAD;

    dAng.x =  gyro.x*dt;//
    dAng.y =  gyro.y*dt;
    dAng.z =  gyro.z*dt;

    //PHI = dAng + 1/12*dAng_oldxdAng
    //+
    dAng0xdAng1  = v3_cross(prevdAng,dAng);

    phi.x = dAng.x + fac*dAng0xdAng1.x;
    phi.y = dAng.y + fac*dAng0xdAng1.y;
    phi.z = dAng.z + fac*dAng0xdAng1.z;

    //q(h) = cos(fai/2) + sin(fai/2)*FAI/fai
    //0.000001 deg = 1.745e-8rad
    ftype vecLength = sqrt(SQR(phi.x) + SQR(phi.y) + SQR(phi.z));
    if(vecLength < 1e-12)//
    {
        qh.q0 = 1.0;
        qh.q1 = 0.0;
        qh.q2 = 0.0;
        qh.q3 = 0.0;
    }
    else
    {
        //Phi/|Phi|
        //
        //q(h) = cos(PHI_NORM/2)+sin(PHI_NORM/2)*(PHI/|PHI|);
        qh.q0 = cos(0.5*vecLength);
        qh.q1 = sin(0.5*vecLength)*phi.x/vecLength;
        qh.q2 = sin(0.5*vecLength)*phi.y/vecLength;
        qh.q3 = sin(0.5*vecLength)*phi.z/vecLength;
    }

    //:Q(t+h) = Q(t)*q(h)
    quat_new.q0 = ahrs_k.q0*qh.q0 - ahrs_k.q1*qh.q1 - ahrs_k.q2*qh.q2 - ahrs_k.q3*qh.q3;
    quat_new.q1 = ahrs_k.q0*qh.q1 + ahrs_k.q1*qh.q0 + ahrs_k.q2*qh.q3 - ahrs_k.q3*qh.q2;
    quat_new.q2 = ahrs_k.q0*qh.q2 - ahrs_k.q1*qh.q3 + ahrs_k.q2*qh.q0 + ahrs_k.q3*qh.q1;
    quat_new.q3 = ahrs_k.q0*qh.q3 + ahrs_k.q1*qh.q2 - ahrs_k.q2*qh.q1 + ahrs_k.q3*qh.q0;

    recipNorm = 1.0/sqrt(SQR(quat_new.q0) + SQR(quat_new.q1) + SQR(quat_new.q2) + SQR(quat_new.q3));
    quat_new.q0 = quat_new.q0*recipNorm;
    quat_new.q1 = quat_new.q1*recipNorm;
    quat_new.q2 = quat_new.q2*recipNorm;
    quat_new.q3 = quat_new.q3*recipNorm;

    //0
    //g/
    acc.x  = psins_raw->acc.x - psins_raw->acc_const_bias.x;
    acc.y  = psins_raw->acc.y - psins_raw->acc_const_bias.y;
    acc.z  = psins_raw->acc.z - psins_raw->acc_const_bias.z;

    s0 = 0.0;s1 = 0.0;s2 = 0.0;s3 = 0.0;

    recipNorm = sqrt(SQR(acc.x)+SQR(acc.y)+SQR(acc.z));
    if(recipNorm>0.98 && recipNorm<1.02)
    {
        //
        recipNorm = 1.0/sqrt(SQR(acc.x)+SQR(acc.y)+SQR(acc.z));
        ax = acc.x*recipNorm;
        ay = acc.y*recipNorm;
        az = acc.z*recipNorm;

        //ahrs_k
        //
        ftype tq0 = 2.0 * ahrs_k.q0;
        ftype tq1 = 2.0 * ahrs_k.q1;
        ftype tq2 = 2.0 * ahrs_k.q2;
        ftype tq3 = 2.0 * ahrs_k.q3;
        ftype fq0 = 4.0 * ahrs_k.q0;
        ftype fq1 = 4.0 * ahrs_k.q1;
        ftype fq2 = 4.0 * ahrs_k.q2;
        ftype eq1 = 8.0 * ahrs_k.q1;
        ftype eq2 = 8.0 * ahrs_k.q2;

        ftype q0q0 = ahrs_k.q0 * ahrs_k.q0;
        ftype q1q1 = ahrs_k.q1 * ahrs_k.q1;
        ftype q2q2 = ahrs_k.q2 * ahrs_k.q2;
        ftype q3q3 = ahrs_k.q3 * ahrs_k.q3;

        // DeltF=JTran x f
        s0 = fq0 * q2q2 + tq2 * ax + fq0 * q1q1 - tq1 * ay;
        s1 = fq1 * q3q3 - tq3 * ax + 4.0 * q0q0 * ahrs_k.q1 - tq0 * ay - fq1 + eq1 * q1q1 + eq1 * q2q2 + fq1 * az;
        s2 = 4.0 * q0q0 * ahrs_k.q2 + tq0 * ax + fq2 * q3q3 - tq3 * ay - fq2 + eq2 * q1q1 + eq2 * q2q2 + fq2 * az;
        s3 = 4.0 * q1q1 * ahrs_k.q3 - tq1 * ax + 4.0 * q2q2 * ahrs_k.q3 - tq2 * ay;

        //DeltF/|DeltF| normalise step magnitude 
        recipNorm = 1.0/sqrt(SQR(s0)  + SQR(s1)  + SQR(s2)  + SQR(s3));
        s0 = s0*recipNorm;
        s1 = s1*recipNorm;
        s2 = s2*recipNorm;
        s3 = s3*recipNorm;
    }

    //Q=Qw-beta*(DeltF/|DeltF|)*deltT
    //beta = 0.0;
    ahrs_k.q0 = quat_new.q0 + (beta*s0)*dt;
    ahrs_k.q1 = quat_new.q1 + (beta*s1)*dt;
    ahrs_k.q2 = quat_new.q2 + (beta*s2)*dt;
    ahrs_k.q3 = quat_new.q3 + (beta*s3)*dt;

    recipNorm = 1.0/sqrt(SQR(ahrs_k.q0) + SQR(ahrs_k.q1) + SQR(ahrs_k.q2) + SQR(ahrs_k.q3));
    ahrs_k.q0 = ahrs_k.q0*recipNorm;
    ahrs_k.q1 = ahrs_k.q1*recipNorm;
    ahrs_k.q2 = ahrs_k.q2*recipNorm;
    ahrs_k.q3 = ahrs_k.q3*recipNorm;

    //if(ahrs_titl_alig == false)
    //{
    //    if(ins_state.yaw_alig_complete == true)//ahrs
    //    {
    
    //        ahrs_k.q0 = ins_state.q0;
    //        ahrs_k.q1 = ins_state.q1;
    //        ahrs_k.q2 = ins_state.q2;
    //        ahrs_k.q3 = ins_state.q3;
    //
   //         ahrs_titl_alig = true;
    //    }
   // }

    q0 = ahrs_k.q0; q1 = ahrs_k.q1; q2 = ahrs_k.q2; q3 = ahrs_k.q3;

    ahrs_k.pitch = RAD2DEG*asin(-2*(q1*q3 - q0*q2));
    ahrs_k.roll  = RAD2DEG*atan2(2*(q2*q3 + q0*q1), 1.0 - 2.0*(q1*q1 + q2*q2));
    ahrs_k.yaw   = RAD2DEG*atan2(2*(q1*q2 + q0*q3), 1.0 - 2.0*(q2*q2 + q3*q3));

    ahrs_k.acc_roll  = RAD2DEG*atan2(-acc.y,-acc.z);
    ahrs_k.acc_pitch = RAD2DEG*asin(acc.x);
}

static uint32_t yaw_unfusion_cnt = 0;
void ahrs_mag_update(SENS_RAW_ST *psins_raw,MAG_RAW_ST *pmag_raw,GNSS_RAW_ST *pgnss_raw)
{ 
    uint16_t i = 0;
    const ftype mg_kp = 0.2;
    ftype q0 = 0.0,q1 = 0.0,q2 = 0.0,q3 = 0.0;
    ftype est_pitch = 0.0,est_roll = 0.0;
    ftype imu_prdic_yaw_k = 0.0;
    ftype sin_roll=0.0,sin_pitch=0.0;
    ftype cos_roll=0.0,cos_pitch=0.0;
    bool twoAntYaw_update = false;

    ftype twoant_meas_yaw = 0.0;
    ftype twoant_yaw_delayed = 0.0;
    ftype mag_meas_yaw_k = 0.0;

    V3_ST mag_ned ={0};
    VNED_ST mag_rot_err={0};

    ftype mag_err_k    = 0.0;
    ftype rot_err_norm = 0.0;

    Q4_ST dq ={0};
    Q4_ST quat_new ={0};

    ftype recipNorm = 0.0;

    //IMU_Update()
	//
    q0 = ahrs_k.q0;q1 = ahrs_k.q1;q2 = ahrs_k.q2;q3 = ahrs_k.q3;

    yaw_unfusion_cnt = yaw_unfusion_cnt + 1;//

    //
    est_pitch = asin(-2*(q1*q3 - q0*q2));
    est_roll  = atan2(2*(q2*q3 + q0*q1),q0*q0 - q1*q1 - q2*q2 + q3*q3);

    //
    imu_prdic_yaw_k    = atan2(2*(q1*q2 + q0*q3),q0*q0 + q1*q1 - q2*q2 - q3*q3);

    sin_roll  =  sin(est_roll); cos_roll  =  cos(est_roll);
    sin_pitch =  sin(est_pitch);cos_pitch =  cos(est_pitch);

    //if(fabs(pgnss_raw->bl - TWOANTBL)<=0.02  && pgnss_raw->hstate==4)//
				if(pgnss_raw->hstate==4)
    {
        twoant_meas_yaw = pgnss_raw->heading*DEG2RAD;//
        twoAntYaw_update =true;
    }
    else
    {
        twoAntYaw_update =false;
    }

    if(pgnss_raw->twoAntYaw_update !=true)//
    {
        twoAntYaw_update =false;
    }

    if(fabs(pgnss_raw->bl - TWOANTBL)<=0.02  && pgnss_raw->hstate==4)//
    {
            twoant_meas_yaw  = pgnss_raw->heading*DEG2RAD;//
            twoAntYaw_update =true;
    }
    else
    {
            twoAntYaw_update =false;
    }

    if(pgnss_raw->twoAntYaw_update !=true)//
    {
        twoAntYaw_update =false;
    }

    //
    if(true == ins_state.yaw_alig_complete && true !=ins_state.need_adjust)
    {
        q0 = ins_state.q0;q1 = ins_state.q1;q2 = ins_state.q2;q3 = ins_state.q3;
        mag_meas_yaw_k = atan2(2*q1*q2 + 2*q0*q3,-2*q2*q2-2*q3*q3+1);
        yaw_unfusion_cnt = 0;
    }
    //
    else if(true == twoAntYaw_update)
    {
        //gnss
        twoant_yaw_delayed = 0.0;
        for(i=0;i<DELAYED_BUF_NUM;i++)
		{
            if(gnss_ang_delayed[i].run_time >= psins_raw->run_time-pgnss_raw->timedelay && 				
                gnss_ang_delayed[i].run_time <= psins_raw->run_time)
			{
                twoant_yaw_delayed = twoant_yaw_delayed + gnss_ang_delayed[i].delt_yaw;
            }
        }

        mag_meas_yaw_k = twoant_meas_yaw + twoant_yaw_delayed;
        yaw_unfusion_cnt = 0;
    }
    //
    else if(yaw_unfusion_cnt>30)
    {
        //if(mag_raw.magYaw_update == true)
        {
            mag_ned.x = cos_pitch*pmag_raw->mag.x + sin_pitch*sin_roll*pmag_raw->mag.y + cos_roll*sin_pitch*pmag_raw->mag.z;
            mag_ned.y = cos_roll*pmag_raw->mag.y  - sin_roll*pmag_raw->mag.z;

            //
            mag_meas_yaw_k = atan2(-mag_ned.y , mag_ned.x);

            yaw_unfusion_cnt = 0;
        }
    }

    //--------------------AHRS-----------------------
    if(mag_meas_yaw_k != 0.0)
    {
        if(mag_meas_yaw_k > PI)
        {
            mag_meas_yaw_k = mag_meas_yaw_k - 2*PI; 
        }
        else if(mag_meas_yaw_k < -PI)
        {
            mag_meas_yaw_k = mag_meas_yaw_k + 2*PI;
        }

        if(ahrs_k.yaw_complete  != true)
        {
            ahrs_k.yaw_complete  = true;//
        }

        // = -
        //
        mag_err_k = mag_meas_yaw_k - imu_prdic_yaw_k;
            
        //atan2(x,y)-PI,+PI
        //-PI~+PI
        //0360
        if(mag_err_k > PI)
        {
            mag_err_k = mag_err_k - 2*PI; 
        }
        else if(mag_err_k < -PI)
        {
            mag_err_k = mag_err_k + 2*PI;
        }
            
        //FAI=ThetaU001
        //
        mag_rot_err.n = 0.0;
        mag_rot_err.e = 0.0;
            
        if(fabs(mag_err_k) > 10*DEG2RAD)//10
        {
            mag_rot_err.d = mag_err_k;
        }
        else
        {
            mag_rot_err.d = mg_kp*mag_err_k;
        }
            
        //0.001 deg = 1.745e-5
        rot_err_norm  = fabs(mag_rot_err.d);//
        if(rot_err_norm < 1e-8)//
        {
            dq.q0 = 1;
            dq.q1 = 0;
            dq.q2 = 0;
            dq.q3 = 0;
        }
        //dq(h) = cos(0.5*fai) + sin(0.5*fai)*FAI/fai
        //U001
        else
        {
            dq.q0 = cos(0.5*rot_err_norm);
            dq.q1 = sin(0.5*rot_err_norm)*mag_rot_err.n/rot_err_norm;
            dq.q2 = sin(0.5*rot_err_norm)*mag_rot_err.e/rot_err_norm;
            dq.q3 = sin(0.5*rot_err_norm)*mag_rot_err.d/rot_err_norm;
        }
               
        //
        //,Q= Q*dq,
        quat_new.q0 = ahrs_k.q0*dq.q0 - ahrs_k.q1*dq.q1 - ahrs_k.q2*dq.q2 - ahrs_k.q3*dq.q3;
        quat_new.q1 = ahrs_k.q1*dq.q0 + ahrs_k.q0*dq.q1 - ahrs_k.q3*dq.q2 + ahrs_k.q2*dq.q3;
        quat_new.q2 = ahrs_k.q2*dq.q0 + ahrs_k.q3*dq.q1 + ahrs_k.q0*dq.q2 - ahrs_k.q1*dq.q3;
        quat_new.q3 = ahrs_k.q3*dq.q0 - ahrs_k.q2*dq.q1 + ahrs_k.q1*dq.q2 + ahrs_k.q0*dq.q3;
            
        //SINS
        recipNorm = 1.0/sqrt(SQR(quat_new.q0) + SQR(quat_new.q1) + SQR(quat_new.q2) + SQR(quat_new.q3));
            
        ahrs_k.q0 = quat_new.q0*recipNorm;//quat_New
        ahrs_k.q1 = quat_new.q1*recipNorm;
        ahrs_k.q2 = quat_new.q2*recipNorm;
        ahrs_k.q3 = quat_new.q3*recipNorm; 

        q0 = ahrs_k.q0;q1 = ahrs_k.q1;q2 = ahrs_k.q2;q3 = ahrs_k.q3;
        ahrs_k.pitch = RAD2DEG*asin(-2*(q1*q3 - q0*q2));
        ahrs_k.roll  = RAD2DEG*atan2(2*(q2*q3 + q0*q1), q0*q0 - q1*q1 - q2*q2 + q3*q3);
        ahrs_k.yaw   = RAD2DEG*atan2(2*(q1*q2 + q0*q3),q0*q0 + q1*q1 - q2*q2 - q3*q3);

        ahrs_k.acc_yaw = RAD2DEG*mag_meas_yaw_k;
    }
   //----------------------AHRS---------------------
}

void ahrs_update(SENS_RAW_ST *psins_raw,MAG_RAW_ST *pmag_raw,GNSS_RAW_ST *pgnss_raw)
{
    ahrs_imu_update(psins_raw,pgnss_raw);
    //ahrs_mag_update(psins_raw,pmag_raw,pgnss_raw);

    ahrs_k.cnt = ahrs_k.cnt+1;
}
