#ifndef __DEVICECONFIG_H__
#define __DEVICECONFIG_H__
#include <stdint.h>

/*
	设备配置文件
			任何一款设备，涉及到硬件的选型配置为固定，在此文件表中选择对应类型			
			实际编程中，根据每种不同的硬件类型单独调试内部数据处理代码
			
	设备拓扑结构：
                                  _________                  _________	
			[ACC]  ->|         |  gpagedata     |         |
                                 |  FPGA   | ----------->   |   ARM   |
			[GYRO] ->|_________|                |_________|
	
	
*/

extern uint8_t Original_Data;//0:正常版本，2:原始数据输出


#define CMPL_CODE_EDWOY         //系统定义
//#define DEVICE_GAODE_622_gd020  //高德测试工装 20241120

//*******************************************************************************************************************
//-------------------------------------------------------------------------------------------------------------------
//
//      设备型号定义
//			下述列表只能选一	
//
//-------------------------------------------------------------------------------------------------------------------
//*******************************************************************************************************************

#define DEVICE_TYPE_370_25J_6089        //6089 光纤陀螺																						//供应LEISHEN无人机，采用6089模块
//#define DEVICE_TYPE_370_25J_355	//355																							//采用355模块
//#define DEVICE_TYPE_600_21A           //MEMS																						//
//#define DEVICE_TYPE_370_25J_DACCxa																								//采用355模块

//-----------分定义--------------------------------------------------------------------------------------------------------

#ifdef DEVICE_TYPE_370_25J_DACCxa
	#define DEVICE_TYPE_370
#endif	

#ifdef DEVICE_TYPE_370_25J_6089
	#define DEVICE_TYPE_370
#endif
#ifdef DEVICE_TYPE_370_25J_355
	#define DEVICE_TYPE_370
#endif

//*******************************************************************************************************************
//-------------------------------------------------------------------------------------------------------------------
//
//      设备硬件模块配置
//			设备型号定义选定后，自动配置完成
//
//-------------------------------------------------------------------------------------------------------------------
//*******************************************************************************************************************


//---------------当前设备采用的“陀螺仪类型”\“加速度计类型”选择---------------
#ifdef DEVICE_TYPE_370_25J_6089
	#define DEVICE_GYRO_TYPE_FOG																										//陀螺仪类型：光纤，分50、60、70、75
	#define DEVICE_ACC_TYPE_hd6089																									//加速度计类型：hd6089
#endif	
#ifdef DEVICE_TYPE_370_25J_355
	#define DEVICE_GYRO_TYPE_FOG
	#define DEVICE_ACC_TYPE_ADLX355																									//加速度计类型：ADLX355	
#endif
#ifdef DEVICE_TYPE_600_21A
	#define DEVICE_GYRO_TYPE_IMU_SCHA63X																						//mems类型陀螺仪，村田IMU（集成MEMS陀螺，内包括陀螺功能）,形状小封装集成方块
		#define DEVICE_GYRO_TYPE_IMU_SCHA63X_subType03																					//mems类型陀螺仪，村田IMU	-后缀型号03
#endif	

#ifdef DEVICE_TYPE_370_25J_DACCxa
	#define DEVICE_GYRO_TYPE_FOG
	#define DEVICE_ACC_TYPE_DACC	
#endif																								//石英加速计类型，暂时每用
																								


 
/*******************************************************************************************************************
//-------------------------------------------------------------------------------------------------------------------
//
//      设备硬件参数配置
//
//-------------------------------------------------------------------------------------------------------------------
********************************************************************************************************************/

//---------------当前设备与上位机422通讯传输采用的“速率”选择---------------

//#define DEVICE_UART_RATE										460800 													  //422串口传输速率类型：460800
#define DEVICE_UART_RATE											2000000														//422串口传输速率类型：2000000

#define DEVICE_UART_SELECT_ISU4_NOU3																							//发送信息422串口，定义则选用UART4，不定义则选用UART3


//*******************************************************************************************************************
//-------------------------------------------------------------------------------------------------------------------
//
//      测试过程注释
//
//-------------------------------------------------------------------------------------------------------------------
//*******************************************************************************************************************

//#define code_test_gyc_old																											//最原始代码										参照：INS370_21J-3mems-runcar-spp&rtk&nfs-hd6089cov-240626-1759-ok-20240627	
#define code_test_gyc_gao_chen_20240708																					    //高给的陈代码，仅供纯惯测试			参照：INS370_21J-New-mdf-hw912-3a1.0-240531-1952-3m-2024-6-15 - 仅供纯惯测试-240705-0910
//#define code_test_gyc_chen_gnsscheck_660sDisable_20240711														  			//超过660S屏蔽GNSS信号，变成无天线纯惯导模式
//----------------输出数据
#define test_gyc_INS912_Output_enabled_20240709																				//422将检测最终结果输出
//#define test_gyc_chen_algdataTo422_20240709																					  	//将压入算法的数据直接通过422发送出去，观察是否抖动丢帧
//#define test_gyc_FPGATo422_20240709																								//将fpga原始数据直接通过422发送出去
//#define test_gyc_FPGATo422_0x00BB_20240716																						//将fpga原始数据直接通过422发送出去： 00BB数据包规则
//#define test_gyc_FPGATo422_0x11BB_20240722																						//将fpga原始数据直接通过422发送出去： 11BB数据包规则
//#define test_gyc_FPGATo422_0x66AA_20240716																						//将fpga原始数据直接通过422发送出去： 66AA数据包规则
//----------------参数
//#define param_gyc_chen_2_GWAW_20240718																							//标度参数采用陈给的第2套参数

#ifdef DEVICE_TYPE_370_25J_355
	#define param_gyc_26j_fogz_fu			//20140814高要求取消，实物调整															//26j设备陀螺Z装反，Z乘以-1	
	#define param_gyc_26j_ACCY_fu																									//26j设备ACC_Y装反，Y乘以-1	
#endif
                                                                                                                                                                                                     //26j设备陀螺Z装反，Z乘以-1        
#ifdef DEVICE_ACC_TYPE_hd6089        //20240807        实际测试时发现x加表数值反，调整
  #define param_gyc_6089_AccX_fu                                                                                                                                                                                                                        //6089设备ACC_X装反，X乘以-1
#endif

//----------------控制LED管脚高低电平变化，以便测试用时

#endif




