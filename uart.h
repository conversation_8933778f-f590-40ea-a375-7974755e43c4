//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：uart.h
// 文件标识：
// 文件摘要：
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.10.16
//---------------------------------------------------------

#ifndef _UART_H
#define _UART_H
#include "board.h"
#include "hpm_clock_drv.h"
#include "hpm_uart_drv.h"
#include "hpm_dmamux_drv.h"
#include "hpm_dma_drv.h"

#define BUFFER_SIZE_TX                                (1024 * 1)
#define BUFFER_SIZE_RX                                (1024 * 1)
#define U4RX_MAXCOUNT                                 (1024 * 4)

#define SDRX_MAXCOUNT                                 (1024 * 2) //(1024) COM3 二进制数据
#define SDRX_BB00_MAXCOUNT                            (336) //(336)	//(1024) BB00原始数据

#define TEST_UART_MAX_BUFFER_SIZE                     (20)
#define TEST_UART                                     HPM_UART14
#define TEST_UART_IRQ                                 IRQn_UART14
#define TEST_UART_CLK_NAME                            clock_uart14
#define TEST_UART_TX_DMA_REQ                          HPM_DMA_SRC_UART14_TX
#define TEST_UART_RX_DMA_REQ                          HPM_DMA_SRC_UART14_RX

#define TEST_UART_DMA_CONTROLLER                      HPM_HDMA
#define TEST_UART_DMAMUX_CONTROLLER                   HPM_DMAMUX
#define TEST_UART_TX_DMA_CHN                          (4U)
#define TEST_UART_RX_DMA_CHN                          (5U)
#define TEST_UART_DMA_IRQ                             IRQn_HDMA

#define SD_UART                                       HPM_UART2
#define SD_UART_IRQ                                   IRQn_UART2
#define SD_UART_CLK_NAME                              clock_uart2

#define SD_UART_TX_DMA_REQ                            HPM_DMA_SRC_UART2_TX
#define SD_UART_RX_DMA_REQ                            HPM_DMA_SRC_UART2_RX

#define SD_UART_DMA_CONTROLLER                        HPM_HDMA
#define SD_UART_DMAMUX_CONTROLLER                     HPM_DMAMUX
#define SD_UART_TX_DMA_CHN                            (6U)
#define SD_UART_RX_DMA_CHN                            (7U)
#define SD_UART_DMA_IRQ                               IRQn_HDMA

extern uint8_t hello_str[SDRX_MAXCOUNT];
extern uint8_t BB00SdData[SDRX_BB00_MAXCOUNT];
// extern uint8_t str[SDRX_MAXCOUNT*2];

extern uint32_t g_baudrate; // 串口波特率
extern uint8_t g_Com3WriteSdFlag; // 写入SD卡文件COM3 二进制数据标志

void UartIrqSendMsg(char *txbuf, int size);
void SDUartIrqSendMsg(char *txbuf, int size);
void analysisRxdata(void);
void sduart_recv_polling(void);
void UartIrqInit(void);
void SDUartIrqInit(void);


#endif