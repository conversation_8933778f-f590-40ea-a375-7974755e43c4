#ifndef ____BSP_GPIO_H____
#define ____BSP_GPIO_H____

#include "main.h"
#include "appmain.h"


//////////////////////////////////////////////////////////////////////////////////////////////////////
//
//////////////////////////////////////////////////////////////////////////////////////////////////////
//
#define LED_SRAM_IO_PORT					GPIO_DO_GPIOA
#define LED_SRAM_IO_PIN						10

#define LED_WHEEL_IO_PORT					GPIO_DO_GPIOA
#define LED_WHEEL_IO_PIN					24

#define LED_STATE_IO_PORT					GPIO_DO_GPIOA
#define LED_STATE_IO_PIN					23

#define CAN1_IO_PORT						GPIO_DO_GPIOB
#define CAN1_IO_PIN							18

//#define CAN2_IO_PORT						GPIOB
//#define CAN2_IO_PIN						GPIO_PIN_15

#define ETH_RST_IO_PORT						GPIO_DO_GPIOA
#define ETH_RST_IO_PIN						11

#define CAN1_STB_IO_PORT					GPIO_DO_GPIOB
#define CAN1_STB_IO_PIN						14

#define CAN2_STB_IO_PORT					GPIO_DO_GPIOA
#define CAN2_STB_IO_PIN						14



//PWM
//#define PWM_IO_PORT							GPIOA //PE27
//#define PWM_IO_PIN							27

//////////////////////////////////////////////////////////////////////////////////////////////////////
//
//////////////////////////////////////////////////////////////////////////////////////////////////////
#define LED_SRAM_ON()				gpio_write_pin(HPM_GPIO0, LED_SRAM_IO_PORT, LED_SRAM_IO_PIN,1)
#define LED_SRAN_OFF()				gpio_write_pin(HPM_GPIO0, LED_SRAM_IO_PORT, LED_SRAM_IO_PIN,0)

#define LED_SRAM_Toggle()			gpio_toggle_pin(HPM_GPIO0, LED_SRAM_IO_PORT,LED_SRAM_IO_PIN)

#define LED_WHEEL_ON()				gpio_write_pin(HPM_GPIO0, LED_WHEEL_IO_PORT, LED_WHEEL_IO_PIN,1)
#define LED_WHEEL_OFF()				gpio_write_pin(HPM_GPIO0, LED_WHEEL_IO_PORT, LED_WHEEL_IO_PIN,0)

#define LED_Wheel_Toggle()			gpio_toggle_pin(HPM_GPIO0, LED_WHEEL_IO_PORT,LED_WHEEL_IO_PIN)

#define LED_STATE_ON()				gpio_write_pin(HPM_GPIO0, LED_STATE_IO_PORT, LED_STATE_IO_PIN,1)
#define LED_STATE_OFF()				gpio_write_pin(HPM_GPIO0, LED_STATE_IO_PORT, LED_STATE_IO_PIN,0)

#define LED_State_Toggle()			gpio_toggle_pin(HPM_GPIO0, LED_STATE_IO_PORT,LED_STATE_IO_PIN)

#define CAN1_Enable()				gpio_write_pin(HPM_GPIO0, CAN1_IO_PORT, CAN1_IO_PIN,1)
#define CAN1_Disable()				gpio_write_pin(HPM_GPIO0, CAN1_IO_PORT, CAN1_IO_PIN,0)

//#define CAN2_Enable()				gpio_bit_set(CAN2_IO_PORT,CAN2_IO_PIN)
//#define CAN2_Disable()				gpio_bit_reset(CAN2_IO_PORT,CAN2_IO_PIN)

#define ETH_RST_High()				gpio_write_pin(HPM_GPIO0, ETH_RST_IO_PORT, ETH_RST_IO_PIN,1)
#define ETH_RST_Low()				gpio_write_pin(HPM_GPIO0, ETH_RST_IO_PORT, ETH_RST_IO_PIN,0)

#define CAN1_STB_High()				gpio_write_pin(HPM_GPIO0, CAN1_STB_IO_PORT, CAN1_STB_IO_PIN,1)
#define CAN1_STB_Low()				gpio_write_pin(HPM_GPIO0, CAN1_STB_IO_PORT, CAN1_STB_IO_PIN,0)

#define CAN2_STB_High()				gpio_write_pin(HPM_GPIO0, CAN2_STB_IO_PORT, CAN2_STB_IO_PIN,1)
#define CAN2_STB_Low()				gpio_write_pin(HPM_GPIO0, CAN2_STB_IO_PORT, CAN2_STB_IO_PIN,0)






void bsp_gpio_init(void);



#endif //____BSP_GPIO_H____
