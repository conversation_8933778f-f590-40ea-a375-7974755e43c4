#ifndef _INS_H_
#define _INS_H_

#include <stdio.h>
#include <stdbool.h>
#include <string.h>
#include <stdint.h>
#include <ctype.h>
#include <math.h>

/*#include <Windows.h>

#pragma float_control(precise, on, push)  // 

#ifdef _DEBUG
    #define printf(...) do { \
        char buffer[256]; \
        sprintf(buffer, __VA_ARGS__); \
        OutputDebugStringA(buffer); \
    } while (0)
#endif*/

#define HIGHPRECISE 1

//#ifdef HIGHPRECISE
    typedef double ftype;
		//typedef float ftype;
//#else
 //   typedef float ftype;
//#endif// HIGHPRECISE 


#ifdef HIGHPRECISE

//#define WIE (360.0/(24*3600.0))

#ifndef PI
#define PI					3.1415926535897932626
#endif

#define GRAVITY_MSS     9.80665// acceleration due to gravity in m/s/s

#define  WGS84_R     6373741.875926// m
#define  WGS84_RE    6378137.0
#define  WGS84_RP    6356752.0; 
#define  WGS84_F    (1.0/298.257223563)

#define DEG2RAD (PI/180.0)  //
#define RAD2DEG (180.0/PI)  //

#define fac     (1.0/12.0)
#else
#define PI  3.1415926535897932f // pi 
#define GRAVITY_MSS     9.80665f// acceleration due to gravity in m/s/s

#define WGS84_R      6373741.875926f// m
#define  WGS84_R     6373741.875926f// m
#define  WGS84_RE    6378137.0f;
#define  WGS84_RP    6356752.0f; 
#define  WGS84_F     (1.0f/298.257223563f);

#define DEG_TO_RAD (PI/180.0f)  //
#define RAD_TO_DEG (180.0f/PI)  //

#define fac     (1.0f/12.0f);
#endif

#define P2_8  256            //2^8
#define P2_16 65536          //2^16
#define P2_32 4294967296     //2^32
#define P2_48 281474976710656//2^48


#define MAX_STATE_DIM  15
#define DELAYED_BUF_NUM ((uint16_t)(150/5))//UM98235ms~100ms,5ms

#define PHIN_STATE_IDX 0//1~3
#define PHIE_STATE_IDX 1
#define PHID_STATE_IDX 2

#define DVEN_STATE_IDX 3//4~6
#define DVEE_STATE_IDX 4
#define DVED_STATE_IDX 5

#define DPSN_STATE_IDX 6//7~9
#define DPSE_STATE_IDX 7
#define DPSD_STATE_IDX 8

#define GBX_STATE_IDX 9//010~12
#define GBY_STATE_IDX 10
#define GBZ_STATE_IDX 11

#define ABX_STATE_IDX 12//013~15
#define ABY_STATE_IDX 13
#define ABZ_STATE_IDX 14

#define VELN_OBS_IDX 0//1~3
#define VELE_OBS_IDX 1
#define VELD_OBS_IDX 2

#define POSN_OBS_IDX 3//1~3
#define POSE_OBS_IDX 4
#define POSD_OBS_IDX 5

//#define TWOANTBL 1.198
extern ftype TWOANTBL;
//-----------------------------------------COMMOM-----------------------------------------------------
typedef union
{
  int32_t  datai;
	uint32_t  datau;
  uint16_t buf[2];
}MYUNION;

typedef union 
{
        uint8_t  byte[2];
        int16_t  iv;
        uint16_t uiv;
}CHAR2TO16;

typedef union 
{
        uint8_t  byte[4];
        float    fv;
        int32_t  iv;
        uint32_t uiv;
}CHAR4TO32;

typedef union 
{
        uint8_t  byte[8];
        double    dv;
        int64_t  iv;
        uint64_t uiv;
}CHAR8TO64;

typedef struct
{
	uint8_t nsf;
	double gnss_afv[3];
	double acc_off[3];
	double gyr_off[3];

}ADJ_STRUCT;


typedef struct
{
	//uint8_t flag;
	uint64_t cnt;
	//float timestamp;

	double wsfl;//
	double wsfr;//
	double wsbl;//
	double wsbr;//

	double wsteer;

	double wodp1;
	double wodp2;

	unsigned char gear;//

}ODO_STRUCT;

typedef struct
{
	uint64_t cnt;//

	uint32_t gnss_week;//GNSS
	uint64_t gnss_tow; //GNSS
	uint8_t  gnss_pps;
	uint8_t  gnss_gps;
	uint8_t  gnss_pps_gps;

	double   mems_gx;//
	double   mems_gy;
	double   mems_gz;
	double   mems_gt;//

	double   mems_ax;//
	double   mems_ay;
	double   mems_az;

	double   gnss_lon;//GNSS
	double   gnss_lat;
	double   gnss_hgt;
	char EW;
	char NS;

	double   gnss_ve;
	double   gnss_vn;
	double   gnss_vu;

	double   twoant_pitch;
	double   twoant_roll;
	double   twoant_yaw;
	double   bl;

	uint8_t   gnss_ps; //GNSS
	uint8_t   gnss_vs; //GNSS

	uint8_t   gnss_rtk_status;//RTK
	uint8_t   gnss_sat;

	ODO_STRUCT canInfo;//
	ADJ_STRUCT adj;

	double gnss_track;//

	uint8_t nav_status;
	uint8_t nav_flag;

	uint8_t imu_select;
	uint8_t mems_type;
	uint8_t use_gps_flag;
	uint8_t fusion_src;
	uint8_t heading_status;

	uint64_t gpssecond982; 

	double fog_gz;//
	double fog_gt;//

}PAOCHE_STRUCT;//332

typedef struct
{
	uint8_t dat_len;//98short
	uint8_t num_clk;
        uint8_t cfg;//
        uint16_t ver;//
  
	ftype mems_tptU;//mems
        ftype mems_tptD;

	ftype mems_gx;//mems
	ftype mems_gy;
	ftype mems_gz;

	ftype mems_ax;//mems
	ftype mems_ay;
	ftype mems_az;

	ftype mems_mx;//mems 
	ftype mems_my;
	ftype mems_mz;

        ftype fog_gx;//
        ftype fog_gy;
        ftype fog_gz;

	ftype fog_tx;//
	ftype fog_ty;
	ftype fog_tz;

        ftype fog_at;//

	ftype fog_ax;//
	ftype fog_ay;
	ftype fog_az;

        ftype gnss_res1;//
        ftype gnss_res2;
        ftype gnss_res3;
        
	uint16_t gnss_week;//GNSS=week*7*24*3600 + tow
	double gnss_tow;
	double gnss_tow2;//GNSS
	double gnss_timedelay;//PPSGNSS

	uint16_t gnss_sat;//

	uint8_t gnss_rtkstate;//RTK0-,4-,5-
         
	uint16_t gnss_vs;//0--1-
	ftype gnss_track;//GNSS
	ftype gnss_vn;//GNSS NEU
	ftype gnss_ve;
	ftype gnss_vu;

	char gnss_ps;//:A-V-

        char gnss_NS;//N-  S=
        double gnss_lat;//GNSS

	char gnss_EW;//E-  W=
	double gnss_lon;

	ftype gnss_hgt;       

	uint8_t twoant_hstate;//0-,4-,5-
	uint8_t gnss_update;//GNSS
	ftype twoant_bl;//
        ftype twoant_roll;
	ftype twoant_pitch;
	ftype twoant_yaw;//
               
	ftype gnss_ecefx_sig;
	ftype gnss_ecefy_sig;
	ftype gnss_ecefz_sig;

	//7
	ftype gnss_gdop;//
	ftype gnss_pdop;//
	ftype gnss_tdop;//
	ftype gnss_vdop;//
	ftype gnss_hdop;//
	ftype gnss_ndop;//
	ftype gnss_edop;//

	ftype gnss_elth;//

	uint16_t can_cnt;//77
        uint16_t can_gears;//76 0- 2- 4-
        double can_flws;//78~79  km/h
        double can_frws;//80~81  km/h
        double can_blws;//82~83  km/h
        double can_brws;//84~85  km/h

        double ins_roll;//
        double ins_pitch;
        double ins_yaw;
        double ins_vn;
        double ins_ve;
        double ins_vd;
        double ins_lat;
        double ins_lon;
        double ins_hgt;
}PAOCHE_FRAME_STRUCT;//INS912

typedef struct
{
    ftype x;
    ftype y;
    ftype z;

}V3_ST;//3

typedef struct
{
    ftype e;
    ftype n;	
    ftype u;

    uint64_t run_time;//ins
}VENU_ST;//ENU3

typedef struct
{
    ftype n;
    ftype e;
    ftype d;

    double run_time;//ins

}VNED_ST;//NED3

typedef struct
{
    ftype n;
    ftype e;
    ftype d;
    float norm;//
}ROTVECTOR_STRUCT;//3

typedef struct
{
    ftype n;//
    ftype e;//
    ftype d;//
    uint64_t run_time;//ins
}ENU_VELOCITY_STRUCT;//

typedef struct
{
    ftype n;//
    ftype e;//   
    ftype d;//
    uint64_t run_time;//ins
}ENU_ANG_STRUCT;//

typedef struct
{
    ftype n;//
    ftype e;//   
    ftype d;//
}VELOCITY_STRUCT;//

typedef struct//double
{
    double lat;//
    double lon;//
    double alt;//
}GEOCOORD_STRUCT;//

typedef struct
{
    ftype roll;
    ftype pitch;
    ftype yaw;

    ftype delt_yaw;//ENU
    double run_time;//ins

}EULER_STRUCT;//

typedef struct
{
    ftype q0;
    ftype q1;
    ftype q2;
    ftype q3;
}Q4_ST;//4

typedef struct
{
    ftype gyro_x;
    ftype gyro_y;
    ftype gyro_z;
}IMU_ST;//4

extern ftype SQR(ftype x);
extern V3_ST v3_cross(V3_ST v1,V3_ST v2);
//-----------------------------------------COMMOM-----------------------------------------------------

//----------------------------------------------------------------------------------
typedef struct
{
	double lon;//deg
	double lat;//deg
	double alt;//m

	ftype pn;//m
	ftype pe;
	ftype pd;

	ftype vn;//NED m/s
	ftype ve;
	ftype vd;

	ftype mag_dec;//
}ORIG_REF_STRUCT;
//----------------------------------------------------------------------------------

//-----------------------------------------SINS-----------------------------------------------------
typedef struct
{
    ftype gyro_x;//x
    ftype gyro_y;//y
    ftype gyro_z;//z

    ftype gyro_x_bias;//x
    ftype gyro_y_bias;//y
    ftype gyro_z_bias;//z

    ftype acc_x;//x
    ftype acc_y;//y
    ftype acc_z;//z

    ftype acc_x_bias;//x
    ftype acc_y_bias;//y
    ftype acc_z_bias;//z

    ftype mag_x;//x
    ftype mag_y;//y
    ftype mag_z;//z

    ftype baro;//
    ftype alt;
    ftype temp;//

}SINS1_SENSOR_DATA_STRUCT;//SINS

typedef struct
{
    V3_ST gyro;//xyz
    V3_ST gyro_const_bias;//xyz

    V3_ST acc;//xyz
    V3_ST acc_const_bias;//xyz
    ftype acc_norm;

    V3_ST mag;//xyz
    V3_ST mag_const_bias;//xyz

    ftype baro;//
    ftype alt;
    ftype temp;//

    ftype dt;

    double run_time;//imu
    double last_time;
}SENS_RAW_ST;//SINS

typedef struct
{
    V3_ST mag;//xyz
    bool magYaw_update;
}MAG_RAW_ST;

typedef struct
{
    ftype q0,q1,q2,q3;//

    V3_ST dAng;//xyz
    V3_ST dVel;  //xyz

    ftype vn;//
    ftype ve;//
    ftype vd;//

    ftype pn;//
    ftype pe;//
    ftype pd;//

    uint64_t run_time;

}SINS_STRUCT;

typedef struct//
{
    uint16_t can_cnt;//77 
    uint16_t can_gears;//76 0- 2- 4-
    double can_flws;//78~79  km/h
    double can_frws;//80~81  km/h
    double can_blws;//82~83  km/h
    double can_brws;//84~85  km/h
}ODO_RAW_ST;
//-----------------------------------------SINS-----------------------------------------------------

//--------------------------------------------------------------------------------------------------------------
#define VIB_BUF_SIZE 15

typedef struct
{
    ftype small_scale_buf[VIB_BUF_SIZE];//buf15
    //buf
    //bufpointpoint+1
    uint16_t small_scale_point;
    ftype small_scale_mean;//buf
    ftype small_scale_var;//


    ftype large_scale_buf[VIB_BUF_SIZE];//buf15
    //buf
    //bufpointpoint+1
    uint16_t large_scale_point;
    ftype large_scale_mean;//
    ftype large_scale_var;//

}VIBRATION_BUF_STRUCT;

extern VIBRATION_BUF_STRUCT nav_yaw;//sins
extern VIBRATION_BUF_STRUCT mag_yaw;//
extern VIBRATION_BUF_STRUCT vel_mod;//

//extern void vib_buff_init();
//extern void vib_buff_update();
//------------------------------------------------------------------------------------------------

//-----------------------------------------AHRS-----------------------------------------
typedef struct
{
	ftype q0;
	ftype q1;
	ftype q2;
	ftype q3;

	ftype roll;
	ftype pitch;
	ftype yaw;

	ftype acc_roll;
	ftype acc_pitch;
	ftype acc_yaw;

	uint32_t cnt;

	bool tilt_complete;
	bool yaw_complete;

}AHRS_ST;//4

//extern QUAT_STRUCT AHRS_Qk;
extern AHRS_ST ahrs_k;//

//true0-AHRS,false0-AHRS
extern bool ahrs_intit_complete;
extern uint8_t imu_sample_cunt;

//extern void ahrs_imu_update(void);
//extern void ahrs_mag_update(void);

//extern void ahrs_init();
//extern void ahrs_update();
//-----------------------------------------AHRS-----------------------------------------

//-----------------------------------------GNSS-------------------------------------------------------

//-----------------------------------------GNSS-------------------------------------------------------
typedef struct
{
	double lat;
	double lon;
	ftype  hgt;

	ftype heading;
	uint16_t heading_cnt;

	ftype vn;
	ftype ve;
	ftype vd;

	ftype wn;
	ftype we;
	ftype wd;

	ftype wie;

	bool heading_have_get;

	uint16_t init_count;
	uint16_t init_cnt;
}ORIG_ST;

typedef struct
{
	uint16_t numSat;
	ftype week;
	ftype tow;
	ftype timedelay;

	uint16_t gnss_vs;//0--1-
        ftype vn;
	ftype ve;
	ftype vd;

	char gnss_ps;//:A-V-
	double lat;
	double lon;
	ftype  hgt;

	//Unicore Reference Commands Manual For N2 High Precison Products_V1_CH_R3.4.pdf P127
	uint8_t rtkstate;//RTK0-,2-4-,5-
	
	char NS;
	char EW;

	uint8_t hstate;
	ftype heading;
	ftype pitch;
	ftype bl;
	ftype track;

	bool pvt_update;
	bool yaw_update;
	bool twoAntYaw_update;
}GNSS_RAW_ST;
//-----------------------------------------INS-----------------------------------------
typedef struct
{
	ftype lon_rad;//
	ftype lat_rad;//
	ftype alt;//

	ftype pn;
	ftype pe;
	ftype pd;

	ftype mag_dec;//
}MAP_PROJ_REF;

typedef struct
{
	ORIG_ST ned_orig;//
	//-----------
	V3_ST deltVk;
	//-----------
	//-------------------------------------
	ftype roll,pitch,yaw;//
	
	ftype q0,q1,q2,q3;//
	//quaternion_t Q;

	ftype vn_last,ve_last,vd_last; // m/s
	ftype vn,ve,vd;// m/s
	ftype pn,pe,pd;//NED,
	double lon,lat,hgt;// 
	//-------------------------------------

	//-------------------------------------
	//------EKF15-----
	VNED_ST rot_err;//
	VNED_ST vel_err;//
	VNED_ST pos_err;//

	V3_ST gyro_bias;//EKF0,
	V3_ST acc_bias;//EKF0
	//------EKF15-----

	//ftype Pk_k_1[15][15];//
	ftype Pk[15][15];//EKF
	//-------------------------------------

	bool yaw_alig_complete;//false- true-

	//1-0-
	bool tilt_alig_complete;

	bool init_alig_complete;

	bool need_reset;
	bool need_adjust;

	ftype dT;
	ftype gn;
	ftype pi;

	ftype mag_meas_yaw_last;
	ftype sins_pred_yaw_last;

	uint64_t row;
	//ftype innovk[9];

	bool ned_origin_have_get;

}INS_STATE_ST;//

typedef struct
{
    ftype nav_q0,nav_q1,nav_q2,nav_q3;   //
    ftype nav_roll,nav_pitch,nav_yaw;    // GNSS,
    ftype ahrs_roll,ahrs_pitch,ahrs_yaw; // GNSS,
    ftype dAng[3]; //  /
    
    ftype vn,ve,vd; // m/s
    //
    //vn,ve,vd
    //
    ftype vx,vy,vz; //  m/s
    
    ftype pn,pe,pd; //m
    
    double Latitude;//
    double Longitude; //
    
    ftype Altitude_to_groud;//m
    ftype MSL_Altitude;//m
    ftype baro;  //

    //------------------------//
    ftype P_horizon_attitude;
    ftype P_yaw; //
    ftype P_velocity;
    ftype P_imu_bias;
    //-----------------------//

    //------------GNSS--------------//
    uint16_t sat_num; //
    uint16_t two_ant_flag; //
    uint64_t unlock_time; //GPS,0-15s,15s
    uint16_t heading_done; //
    //------------GNSS--------------//

}OUT_RESULT_STRUCT;//

extern bool inscanruning;

extern uint64_t row;

extern uint16_t gnss_ang_delay_num;
extern uint16_t gnss_vel_delay_num;
extern uint16_t gnss_pos_delay_num;

extern EULER_STRUCT gnss_ang_delayed[DELAYED_BUF_NUM];
extern VNED_ST gnss_vel_delayed[DELAYED_BUF_NUM];
extern VNED_ST gnss_pos_delayed[DELAYED_BUF_NUM];

extern bool ENU_origin_have_get;
extern ORIG_REF_STRUCT enu_orig;//


extern bool yaw_alig_complete;
extern bool init_alig_complete;

extern bool ins_result_ok;
extern bool ins_need_reset;
extern bool ins_state_need_adjust;

//extern PAOCHE_STRUCT paochedata;
extern PAOCHE_FRAME_STRUCT paochedata;
extern INS_STATE_ST ins_state;
extern OUT_RESULT_STRUCT out_result;

extern unsigned short adlxdata[12];
extern uint8_t ADXL355_CMD[8];
extern void uart7sendmsg2(uint8_t *txbuf, int size);
extern bool adxl355_is_running;
extern void ADXL355_UART7_Init(void);
extern void l355_uart_recv_polling(void);

//extern void decode_paoche(FILE *paocheobs_fp,PAOCHE_FRAME_STRUCT *paocheframe);
extern void decode_paoche(char *pbuf,PAOCHE_FRAME_STRUCT *paocheframe);
extern void  fmc2sinsraw(uint16_t *tempbuf16,PAOCHE_FRAME_STRUCT *paocheframe);

extern ftype iggii(ftype v,ftype var,ftype c);
extern ftype state_constrain(ftype state,ftype min,ftype max);

extern bool gnss_quality_check(GNSS_RAW_ST *pgnss_raw);  
extern bool gnss_pos_check(GNSS_RAW_ST *pgnss_raw);
extern bool gnss_vel_check(GNSS_RAW_ST *pgnss_raw);

extern VNED_ST geo2ned(double *geo,double *orig_geo);
extern void ned2geo(VNED_ST *ned,double *geo_pos,double *geo);

//extern bool get_enu_orig(nav_pvt *gnss_pvh);

extern void ahrs_init(SENS_RAW_ST *psins_raw,MAG_RAW_ST *pmag_raw,GNSS_RAW_ST *pgnss_raw,uint16_t init_cnt);
extern void ahrs_imu_update(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw);
extern void ahrs_update(SENS_RAW_ST *psins_raw,MAG_RAW_ST *pmag_raw,GNSS_RAW_ST *pgnss_raw);

extern void sins_pk_update(SENS_RAW_ST *psins_raw);
extern void sins_state_update(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw);//

extern void fuseTwoAntHeading(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw);
extern void fuseGnssYaw(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw);// ;
extern void fuseGnssVelPosHeight(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw);
//extern void fuseBaroHeight(void);//
extern void fuseGnssVel(SENS_RAW_ST *psins_raw,GNSS_RAW_ST *pgnss_raw);

extern void ins_reset(AHRS_ST *pahrs_k,GNSS_RAW_ST *pgnss_raw);
extern void ins_init(AHRS_ST *pahrs_k,GNSS_RAW_ST *pgnss_raw);
extern void ins_update(void);
//-----------------------------------------INS-----------------------------------------
#endif
