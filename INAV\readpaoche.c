
#include "appmain.h"
#include "ins.h"


//天陆海跑车协议的状态机定义
typedef enum
{
	RCV_DATA_LEN  = 1,//接收帧头:2个字节表示数据包长度0x0062
	RCV_DATA_CNT  = 2,//接收帧编号:2个字节
  RCV_DATA_VER  = 3,//接收版本号
	RCV_DATA_VAL  = 4,//接收有效数据  
	RCV_DATA_CRC  = 5//接收校验码 
}TLH_RCV_STATE;


PAOCHE_FRAME_STRUCT paocheframe ={0};

unsigned int  myget_8bit_I16(uint8_t *msgbuff)
{   
    CHAR2TO16 temp ={0};
    temp.byte[0] = msgbuff[0];
    temp.byte[1] = msgbuff[1];
    return temp.iv;
}

int32_t  myget_16bit_I32(uint8_t *msgbuff)
{   
    CHAR4TO32 temp ={0};
    temp.byte[0] = msgbuff[0];
    temp.byte[1] = msgbuff[1];
    temp.byte[2] = msgbuff[2];
    temp.byte[3] = msgbuff[3];
    return temp.iv;
}

//unsigned int  get_16bit_I16(uint8_t *msgbuff)
//{   
//
//}

float myget_16bit_D32(uint8_t *msgbuff)
{   
    CHAR4TO32 temp ={0};
    temp.byte[0] = msgbuff[0];
    temp.byte[1] = msgbuff[1];
    temp.byte[2] = msgbuff[2];
    temp.byte[3] = msgbuff[3];
    return temp.fv;
}

double myget_16bit_D64(uint8_t *msgbuff)
{        
    CHAR8TO64 temp ={0};
    temp.byte[0] = msgbuff[0];
    temp.byte[1] = msgbuff[1];
    temp.byte[2] = msgbuff[2];
    temp.byte[3] = msgbuff[3];
    temp.byte[4] = msgbuff[4];
    temp.byte[5] = msgbuff[5];
    temp.byte[6] = msgbuff[6];
    temp.byte[7] = msgbuff[7];
    return temp.dv;
}
bool inscanruning = false;
TLH_RCV_STATE rcv_state = RCV_DATA_LEN;
//void  fmc2sinsraw(uint16_t *tempbuf16,SENSOR_RAW_DATA_STRUCT *psins_raw)
void  fmc2sinsraw(uint16_t *tempbuf16,PAOCHE_FRAME_STRUCT *paocheframe)
{
	  uint8_t i = 0;
	  uint8_t temp2023 = 0;
	  uint32_t tempcrc = 0;
	  bool fmcisok = false;
	  uint8_t temp33[8]     = {0};//8字节补码,64位
		char temp88_buf[5]    = {0};
		uint32_t temp_buf_s   = 0;
	  uint64_t temp64_buf_s = 0;
		
		//PAOCHE_FRAME_STRUCT paocheframe;
		
		//double fs = 200;//200HZ
    //double gn =  9.780325;//当地重力加速度
		
//		double fs =  200.0;
//		double gn =  1.0;
		
		//double coe_fog_gx = 751071.479222854;//0001机器
    //double coe_fog_gy = 755221.579688000;
    //double coe_fog_gz = 753683.896804558;
		
		//double bwx = -492.390164964;//0001机器0偏
    //double bwy = -367.485595182;
    //double bwz = -491.921075540;
		
	  double coe_fog_gx = 1.0;//0001机器
    double coe_fog_gy = 1.0;
    double coe_fog_gz = 1.0;
		
		double bwx =  0.0;//0001机器0偏
    double bwy =  0.0;
    double bwz =  0.0;
		
		MYUNION temp99 = {0};
		double coe_fog_hd6089 = 0.0000025;//1/400000
		
		for(i=0;i<=111;i++)
		{
			switch(rcv_state)
			{
					case RCV_DATA_LEN://接收表示长度的字段
							 if(tempbuf16[i]==0x6F)//长度正确
							 {
								  tempcrc = tempcrc + tempbuf16[i];
									rcv_state = RCV_DATA_CNT;
							 }
							 else
							 {
									rcv_state = RCV_DATA_LEN;
							 }
							 break;
							 
					case RCV_DATA_CNT:
						   temp2023 = (uint8_t)(tempbuf16[i]>>8);
						   if(temp2023<=19)
							 {
								  tempcrc = tempcrc + tempbuf16[i];
									rcv_state = RCV_DATA_VER;
							 }
							 else
							 {
									rcv_state = RCV_DATA_LEN;
							 }
							 break;
							 
					case RCV_DATA_VER:
							 if(tempbuf16[i]==0x0101)//版本号正确
							 {
								  tempcrc = tempcrc + tempbuf16[i];
									rcv_state = RCV_DATA_VAL;
							 }
							 else
							 {
									rcv_state = RCV_DATA_LEN;
							 }
						   break;
							 
					case RCV_DATA_VAL:
						   if(i<98)
							 {
								  tempcrc = tempcrc + tempbuf16[i];
								  rcv_state = RCV_DATA_VAL;
							 }
							 else
							 {
								  tempcrc = tempcrc + tempbuf16[i];
									rcv_state = RCV_DATA_CRC;
							 }
						   break;
							 
					case RCV_DATA_CRC:
						   tempcrc = tempcrc & 0x0000ffff;
					     if(tempcrc==tempbuf16[111])
							 {
									fmcisok = true;
							 }
						   rcv_state = RCV_DATA_LEN;
					
					default:
							 rcv_state = RCV_DATA_LEN;
							 break;
			}
		}
			
		paocheframe->dat_len = (uint8_t)tempbuf16[1-1];
	
		//temp_buf_s = tempbuf16[2-1];
		paocheframe->cfg = 0;	//(uint8_t)(temp_buf_s & 0x000000ff);//提取低8位
		
		//temp_buf_s =  temp_buf_s>>8;
		paocheframe->num_clk = tempbuf16[2-1];	//20240814	根据FMC1.21修改，帧计数500帧可兼容	//(uint8_t)temp_buf_s;//高8位有效,两个GNSS PPS之间的帧序列号
		
		paocheframe->ver     = tempbuf16[3-1];//版本号
		
	  temp_buf_s = (uint32_t)tempbuf16[5-1]*P2_16 + (uint32_t)tempbuf16[4-1];//转变成有符号数
		//tempdata(fram_cnt,2) = 25.0 + (temp_buf_s/30.0);//IMU温度
																temp33[1-1]  =  temp_buf_s & 0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		int32_t xx = myget_16bit_I32(temp33);
		paocheframe->mems_tptU = 25.0f + (double)xx/30.0f;//MEMS IMU温度
		
		temp_buf_s = tempbuf16[6-1];//转变成有符号数
																	 temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s   =   temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		xx = myget_8bit_I16(temp33);
		//paocheframe->mems_gx = (double)xx/160.0f;//MEMS IMU陀螺X轴
		
		temp_buf_s = tempbuf16[7-1];//转变成有符号数
																temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		xx = myget_8bit_I16(temp33);
		//paocheframe->mems_gy = (double)xx/160.0f;//MEMS IMU陀螺Y轴
		
		temp_buf_s = tempbuf16[8-1];//转变成有符号数
																 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8; temp33[2-1]  =  temp_buf_s&0x000000ff;
		xx = myget_8bit_I16(temp33);
		//paocheframe->mems_gz = (double)xx/160.0f;//MEMS IMU陀螺Z轴
		
		temp_buf_s = tempbuf16[9-1];//转变成有符号数                                   
																temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		xx = myget_8bit_I16(temp33);
		paocheframe->mems_ax = (double)xx/4905.0f;//MEMS IMU加表X轴
		
		temp_buf_s = tempbuf16[10-1];//转变成有符号数
		temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		xx = myget_8bit_I16(temp33);
		paocheframe->mems_ay = (double)xx/4905.0f;//MEMS IMU加表Y轴
		
	  temp_buf_s = tempbuf16[11-1];//转变成有符号数
																temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		xx = myget_8bit_I16(temp33);
		paocheframe->mems_az = (double)xx/4905.0f;//MEMS IMU加表Z轴
		
		paocheframe->mems_mx =(int16_t)tempbuf16[12-1];//X轴磁力计
		paocheframe->mems_my =(int16_t)tempbuf16[13-1];//Y轴磁力计
		paocheframe->mems_mz =(int16_t)tempbuf16[14-1];//Z轴磁力计
		
		temp_buf_s = (uint32_t)tempbuf16[16-1]*P2_16 + (uint32_t)tempbuf16[15-1];
															 temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;
		paocheframe->fog_gx =  ((double)myget_16bit_I32(temp33)-bwx)/coe_fog_gx;//光纤陀螺X轴
		
	  temp_buf_s = (uint32_t)tempbuf16[18-1]*P2_16 + (uint32_t)tempbuf16[17-1];
															 temp33[1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff; 
		paocheframe->fog_gy = ((double)myget_16bit_I32(temp33)-bwy)/coe_fog_gy;//光纤陀螺Y轴
		
	  temp_buf_s = (uint32_t)tempbuf16[20-1]*P2_16 + (uint32_t)tempbuf16[19-1];
																					temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;   
		paocheframe->fog_gz  =((double)myget_16bit_I32(temp33)-bwz)/coe_fog_gz;//光纤陀螺Z轴
		
		//paocheframe->fog_tx = (double)((int16_t)tempbuf16[21-1])*0.0625f;//光纤陀螺X轴温度
		//paocheframe->fog_ty = (double)((int16_t)tempbuf16[22-1])*0.0625f;//光纤陀螺Y轴温度
		//paocheframe->fog_tz = (double)((int16_t)tempbuf16[23-1])*0.0625f;//光纤陀螺Z轴温度
		
//		temp_buf_s = (uint32_t)tempbuf16[25-1]*P2_16 + (uint32_t)tempbuf16[24-1];
//																					temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
//		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
//		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
//		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;
//		paocheframe->fog_ax = gn*fs*myget_16bit_D32(temp33);//石英加表X轴

//		temp_buf_s = (uint32_t)tempbuf16[27-1]*P2_16 + (uint32_t)tempbuf16[26-1];
//																					temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
//		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
//		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
//		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;
//		paocheframe->fog_ay = gn*fs*myget_16bit_D32(temp33);//石英加表Y轴

//		temp_buf_s = (uint32_t)tempbuf16[29-1]*P2_16 + (uint32_t)tempbuf16[28-1];
//															 temp33[1]    =  temp_buf_s&0x000000ff;//提取低8位
//		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
//		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
//		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
//		paocheframe->fog_az = gn*fs*myget_16bit_D32(temp33);//石英加表Z轴
		
		//--------------HD6089-----------------------------------------------------------
		temp_buf_s = (uint32_t)tempbuf16[25-1]*P2_16 + (uint32_t)tempbuf16[24-1];

                temp99.datau = temp_buf_s;
		temp99.datau = temp99.datau<<8;
		temp99.datai = temp99.datai >>8;
		paocheframe->fog_ax = temp99.datai*coe_fog_hd6089;//HD6089加表X轴

		temp_buf_s = (uint32_t)tempbuf16[27-1]*P2_16 + (uint32_t)tempbuf16[26-1];
    temp99.datau = temp_buf_s;
		temp99.datau = temp99.datau<<8;
		temp99.datai = temp99.datai >>8;
		paocheframe->fog_ay = temp99.datai*coe_fog_hd6089;//HD6089加表Y轴

		temp_buf_s = (uint32_t)tempbuf16[29-1]*P2_16 + (uint32_t)tempbuf16[28-1];
    temp99.datau = temp_buf_s;
		temp99.datau = temp99.datau<<8;
		temp99.datai = temp99.datai >>8;
		paocheframe->fog_az = temp99.datai*coe_fog_hd6089;//HD6089加表Z轴
		//--------------HD6089-----------------------------------------------------------
		
		//paocheframe->fog_at = (double)((int32_t)tempbuf16[30-1]*0.01);//石英加表温度

		//temp_buf_s = tempbuf16[8-1];//转变成有符号数
		//paocheframe->gnss_res1 = tempbuf16[31-1];//预留1
		//paocheframe->gnss_res2 = tempbuf16[32-1];
		//paocheframe->gnss_res3 = tempbuf16[33-1];
		
	  paocheframe->gnss_week = tempbuf16[34-1];//GNSS周,无符号数
		
		temp_buf_s = (uint64_t)tempbuf16[36-1]*P2_16 + (uint64_t)tempbuf16[35-1];//接收机输出的GNSS周内秒,无符号数,单位是ms
		paocheframe->gnss_tow = (double)temp_buf_s*1e-3;//天陆海在tow基础上每个周期加5ms

		temp_buf_s = (uint32_t)tempbuf16[38-1]*P2_16 +( uint32_t)tempbuf16[37-1];
		paocheframe->gnss_tow2 = (double)temp_buf_s*1e-3;//接收机输出的周内秒

		temp_buf_s = (uint32_t)tempbuf16[40-1]*P2_16 + (uint32_t)tempbuf16[39-1];
		paocheframe->gnss_timedelay = (double)temp_buf_s*1e-8;//PPS到GNSS报文输出的延时单位是10ns

		paocheframe->gnss_sat = tempbuf16[41-1];//搜星数
		
		//和芯星通7.3.15 AGRIC 信息
		//RTK状态0-无效解,1-单点定位解,2-伪距差分解，4-固定解,5-浮点解
		temp_buf_s = tempbuf16[42-1];
		temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;//提取高8位 
		paocheframe->gnss_rtkstate = temp33[2-1];//RTK状态

		//GNSS速度状态：0-有效，1-无效
		temp_buf_s = tempbuf16[43-1];
															 temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;
                temp33[2-1]  =   temp_buf_s&0x000000ff;//提取高8位
		paocheframe->gnss_vs = temp33[2-1];

                temp_buf_s = tempbuf16[43-1];
                temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
                paocheframe->gnss_update = temp33[1-1];//增加GNSS更新状态
		if(paocheframe->gnss_update ==1)
		{
			int a=0;
			a = 1;
		
		}

		 //8位ASCII码先转换成字符串再转浮点数
		temp_buf_s = tempbuf16[44-1];
															temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;//提取高8位

		temp88_buf[5-1] = (char)temp33[1-1];
		temp88_buf[4-1] = (char)temp33[2-1];

		temp_buf_s = tempbuf16[45-1];
																 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;  temp33[2-1]  =   temp_buf_s&0x000000ff;

		temp88_buf[3-1] = (char)temp33[1-1];
		temp88_buf[2-1] = (char)temp33[2-1];

		temp_buf_s = tempbuf16[46-1];
																 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;  temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp88_buf[1-1] = (char)temp33[2-1];
		paocheframe->gnss_track =  strtod(temp88_buf,NULL);//track true

		temp_buf_s = (uint32_t)tempbuf16[48-1]*P2_16 +  (uint32_t)tempbuf16[47-1];//速度N分量
																temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;    
		paocheframe->gnss_vn = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[50-1]*P2_16 + (uint32_t)tempbuf16[49-1];//速度E分量
																						temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;  
		paocheframe->gnss_ve = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[52-1]*P2_16 + (uint32_t)tempbuf16[51-1];//速度U分量 
																temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s =  temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s =  temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;  
		paocheframe->gnss_vu = myget_16bit_D32(temp33);

		temp_buf_s = tempbuf16[53-1];
		temp_buf_s = (temp_buf_s>>8)&0x00FF;//提取高8位
		paocheframe->gnss_ps = (char)temp_buf_s;//位置状态:A-有效，V-无效

		temp_buf_s = tempbuf16[53-1];
		temp_buf_s = temp_buf_s;
		paocheframe->gnss_NS = (char)temp_buf_s;//纬度方向N-北纬  S=南纬

		temp64_buf_s = (uint64_t)tempbuf16[57-1]*P2_48 + (uint64_t)tempbuf16[56-1]*P2_32 + (uint64_t)tempbuf16[55-1]*P2_16 + (uint64_t)tempbuf16[54-1];//纬度
																	 temp33[1-1]  =   temp64_buf_s&0x000000ff;//提取低8位
		temp64_buf_s = temp64_buf_s>>8;temp33[2-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[3-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[4-1]  =   temp64_buf_s&0x000000ff; 
		temp64_buf_s = temp64_buf_s>>8;temp33[5-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[6-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[7-1]  =   temp64_buf_s&0x000000ff; 
		temp64_buf_s = temp64_buf_s>>8;temp33[8-1]  =   temp64_buf_s&0x000000ff; 
		paocheframe->gnss_lat = myget_16bit_D64(temp33);

		temp_buf_s = tempbuf16[58-1];
		temp_buf_s = temp_buf_s>>8;//提取高8位
		paocheframe->gnss_EW = (char)temp_buf_s;//经度方向E-东经  W=西经

                temp_buf_s = tempbuf16[58-1];
		temp33[1-1]  = temp_buf_s&0x00ff;//提取低8位
                paocheframe->twoant_hstate = temp33[1-1];//航向状态0-无效解,4-固定解,5-浮点解


		temp64_buf_s = (uint64_t)tempbuf16[62-1]*P2_48 + (uint64_t)tempbuf16[61-1]*P2_32 + (uint64_t)tempbuf16[60-1]*P2_16 + (uint64_t)tempbuf16[59-1];//经度
																	 temp33[1-1]  =  temp64_buf_s&0x000000ff;//提取低8位
		temp64_buf_s = temp64_buf_s>>8;temp33[2-1]  =  temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[3-1]  =  temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[4-1]  =  temp64_buf_s&0x000000ff; 
		temp64_buf_s = temp64_buf_s>>8;temp33[5-1]  =  temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[6-1]  =  temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[7-1]  =  temp64_buf_s&0x000000ff; 
		temp64_buf_s = temp64_buf_s>>8;temp33[8-1]  =  temp64_buf_s&0x000000ff;              
		paocheframe->gnss_lon = myget_16bit_D64(temp33);

		temp64_buf_s = (uint64_t)tempbuf16[66-1]*P2_48 + (uint64_t)tempbuf16[65-1]*P2_32 + (uint64_t)tempbuf16[64-1]*P2_16 + (uint64_t)tempbuf16[63-1];//高度
																	 temp33[1-1]  =   temp64_buf_s&0x000000ff;//提取低8位
		temp64_buf_s = temp64_buf_s>>8;temp33[2-1]  =  temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[3-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[4-1]  =   temp64_buf_s&0x000000ff; 
		temp64_buf_s = temp64_buf_s>>8;temp33[5-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[6-1]  =   temp64_buf_s&0x000000ff;
		temp64_buf_s = temp64_buf_s>>8;temp33[7-1]  =  temp64_buf_s&0x000000ff; 
		temp64_buf_s = temp64_buf_s>>8;temp33[8-1]  =   temp64_buf_s&0x000000ff;                 
		paocheframe->gnss_hgt = myget_16bit_D64(temp33);


		//temp_buf_s =   temp_buf_s>>8;
  //              temp33[2-1]  =   temp_buf_s&0x000000ff; 
		//paocheframe->twoant_hstate = temp33[2-1];//航向状态0-无效解,4-固定解,5-浮点解

		temp_buf_s =( uint32_t)tempbuf16[68-1]*P2_16 + (uint32_t)tempbuf16[67-1];//RTK基线长度
															 temp33[1-1]=  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;      
		paocheframe->twoant_bl = myget_16bit_D32(temp33);

		 temp_buf_s = (uint32_t)tempbuf16[69]*P2_16 + (uint32_t)tempbuf16[68];//横滚角
															 temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;  
		paocheframe->twoant_roll = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[71]*P2_16 + (uint32_t)tempbuf16[70];//俯仰角
															 temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1] =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1] =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1] =   temp_buf_s&0x000000ff;  
		paocheframe->twoant_pitch = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[73]*P2_16 + (uint32_t)tempbuf16[72];//航向角
															 temp33[1-1]  =   temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =   temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =   temp_buf_s&0x000000ff;  
		paocheframe->twoant_yaw = myget_16bit_D32(temp33);
		
		temp_buf_s = (uint32_t)tempbuf16[78-1]*P2_16 + (uint32_t)tempbuf16[77-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_ecefx_sig = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[80-1]*P2_16 + (uint32_t)tempbuf16[79-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_ecefy_sig = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[82-1]*P2_16 + (uint32_t)tempbuf16[81-1];
															 temp33[1-1]    =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_ecefz_sig = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[84-1]*P2_16 + (uint32_t)tempbuf16[83-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  = temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_gdop = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[86-1]*P2_16 + (uint32_t)tempbuf16[85-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_pdop = myget_16bit_D32(temp33);

		 temp_buf_s = (uint32_t)tempbuf16[88-1]*P2_16 + (uint32_t)tempbuf16[87-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_tdop = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[90-1]*P2_16 + (uint32_t)tempbuf16[89-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_vdop = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[92-1]*P2_16 + (uint32_t)tempbuf16[91-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1] =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_hdop = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[94-1]*P2_16 + (uint32_t)tempbuf16[93-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_ndop = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[96-1]*P2_16 + (uint32_t)tempbuf16[95-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_edop = myget_16bit_D32(temp33);

		temp_buf_s = (uint32_t)tempbuf16[98-1]*P2_16 + (uint32_t)tempbuf16[97-1];
															 temp33[1-1]  =  temp_buf_s&0x000000ff;//提取低8位
		temp_buf_s = temp_buf_s>>8;temp33[2-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[3-1]  =  temp_buf_s&0x000000ff;
		temp_buf_s = temp_buf_s>>8;temp33[4-1]  =  temp_buf_s&0x000000ff;
		//paocheframe->gnss_elth = myget_16bit_D32(temp33);//GNSS选星仰角	
				
		//天陆海是ZXY转NED老机型
		//psins_raw->gyro.x =  paocheframe.fog_gx;//陀螺
		//psins_raw->gyro.y = -paocheframe.fog_gz;
		//psins_raw->gyro.z =  paocheframe.fog_gy;
		
		//psins_raw->acc.x =  paocheframe.fog_ay;//加表
		//psins_raw->acc.y =  paocheframe.fog_ax;
		//psins_raw->acc.z = -paocheframe.fog_az;
		
		//天陆海是ZXY转NED新小的机型
		//psins_raw->gyro.x =  paocheframe.fog_gx;//陀螺
		//psins_raw->gyro.y =  paocheframe.fog_gy;
		//psins_raw->gyro.z =  paocheframe.fog_gz;
		
		//psins_raw->acc.x =  paocheframe.fog_ax;//西安加表
		//psins_raw->acc.y = -paocheframe.fog_ay;
		//psins_raw->acc.z = -paocheframe.fog_az;
		
		
		temp_buf_s = (uint32_t)tempbuf16[6]*P2_16 + (uint32_t)tempbuf16[5];
		temp99.datau = temp_buf_s;
		//psins_raw->adx355.x = temp99.datai/64000.0;
		paocheframe->mems_ax  = temp99.datai/64000.0;
		
		temp_buf_s = (uint32_t)tempbuf16[8]*P2_16 + (uint32_t)tempbuf16[7];
		temp99.datau = temp_buf_s;
		//psins_raw->adx355.y = temp99.datai/64000.0;
		paocheframe->mems_ay  = temp99.datai/64000.0;
		
		temp_buf_s = (uint32_t)tempbuf16[10]*P2_16 + (uint32_t)tempbuf16[9];
		temp99.datau = temp_buf_s;
		//psins_raw->adx355.z = temp99.datai/64000.0;
		paocheframe->mems_az  = temp99.datai/64000.0;
		
		//psins_raw->acc.x =   paocheframe.mems_ax;//ADXL355加表
		//psins_raw->acc.y =  -paocheframe.mems_ay;
		//psins_raw->acc.z =  -paocheframe.mems_az;
		
		if(inscanruning==false)
		{
			if(fmcisok == true && paocheframe->gnss_rtkstate>=1 && paocheframe->twoant_hstate==4&&
				 paocheframe->gnss_vs==0&& paocheframe->gnss_ps=='A')
			{
				inscanruning = true;
			}		
		}
}
